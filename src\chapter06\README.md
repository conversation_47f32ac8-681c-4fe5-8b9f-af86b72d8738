# 第6章：泛型 (Generics)

## 6.1 泛型基础

泛型允许我们创建可重用的组件，这些组件可以处理多种类型而不是单一类型。

### 基本泛型函数

```typescript
// 不使用泛型的版本
function identityNumber(arg: number): number {
    return arg;
}

function identityString(arg: string): string {
    return arg;
}

// 使用泛型的版本
function identity<T>(arg: T): T {
    return arg;
}

// 使用方式
let output1 = identity<string>("myString");
let output2 = identity<number>(100);
let output3 = identity("myString"); // 类型推断
```

### 泛型接口

```typescript
interface GenericIdentityFn<T> {
    (arg: T): T;
}

function identity<T>(arg: T): T {
    return arg;
}

let myIdentity: GenericIdentityFn<number> = identity;
```

## 6.2 泛型类

```typescript
class GenericNumber<T> {
    zeroValue: T;
    add: (x: T, y: T) => T;
    
    constructor(zeroValue: T, addFn: (x: T, y: T) => T) {
        this.zeroValue = zeroValue;
        this.add = addFn;
    }
}

let myGenericNumber = new GenericNumber<number>(0, (x, y) => x + y);
let myGenericString = new GenericNumber<string>("", (x, y) => x + y);
```

## 6.3 泛型约束

### 基本约束

```typescript
interface Lengthwise {
    length: number;
}

function loggingIdentity<T extends Lengthwise>(arg: T): T {
    console.log(arg.length); // 现在我们知道它有length属性
    return arg;
}

// 使用
loggingIdentity("hello"); // OK
loggingIdentity([1, 2, 3]); // OK
loggingIdentity({ length: 10, value: 3 }); // OK
// loggingIdentity(3); // Error, number没有length属性
```

### 在泛型约束中使用类型参数

```typescript
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
    return obj[key];
}

let x = { a: 1, b: 2, c: 3, d: 4 };

getProperty(x, "a"); // OK
getProperty(x, "b"); // OK
// getProperty(x, "m"); // Error: 'm'不存在于类型'{a: number, b: number, c: number, d: number}'中
```

## 6.4 条件类型

```typescript
// 基本条件类型
type TypeName<T> = 
    T extends string ? "string" :
    T extends number ? "number" :
    T extends boolean ? "boolean" :
    T extends undefined ? "undefined" :
    T extends Function ? "function" :
    "object";

type T0 = TypeName<string>; // "string"
type T1 = TypeName<"a">; // "string"
type T2 = TypeName<true>; // "boolean"
type T3 = TypeName<() => void>; // "function"
type T4 = TypeName<string[]>; // "object"
```

### 分布式条件类型

```typescript
type Diff<T, U> = T extends U ? never : T;
type Filter<T, U> = T extends U ? T : never;

type T30 = Diff<"a" | "b" | "c" | "d", "a" | "c" | "f">; // "b" | "d"
type T31 = Filter<"a" | "b" | "c" | "d", "a" | "c" | "f">; // "a" | "c"
```

## 6.5 映射类型

### 基本映射类型

```typescript
type Readonly<T> = {
    readonly [P in keyof T]: T[P];
};

type Partial<T> = {
    [P in keyof T]?: T[P];
};

type Required<T> = {
    [P in keyof T]-?: T[P];
};

interface Person {
    name: string;
    age?: number;
}

type ReadonlyPerson = Readonly<Person>;
type PartialPerson = Partial<Person>;
type RequiredPerson = Required<Person>;
```

### 高级映射类型

```typescript
// Pick - 选择特定属性
type Pick<T, K extends keyof T> = {
    [P in K]: T[P];
};

// Omit - 排除特定属性
type Omit<T, K extends keyof any> = Pick<T, Exclude<keyof T, K>>;

// Record - 创建具有特定键值类型的对象类型
type Record<K extends keyof any, T> = {
    [P in K]: T;
};

interface Todo {
    title: string;
    description: string;
    completed: boolean;
}

type TodoPreview = Pick<Todo, "title" | "completed">;
type TodoInfo = Omit<Todo, "completed">;
type TodoRecord = Record<"todo1" | "todo2", Todo>;
```

## 6.6 实用工具类型

### 内置工具类型

```typescript
interface User {
    id: number;
    name: string;
    email: string;
    password: string;
}

// Partial<T> - 所有属性可选
type PartialUser = Partial<User>;

// Required<T> - 所有属性必需
type RequiredUser = Required<User>;

// Readonly<T> - 所有属性只读
type ReadonlyUser = Readonly<User>;

// Pick<T, K> - 选择特定属性
type UserPublicInfo = Pick<User, "id" | "name" | "email">;

// Omit<T, K> - 排除特定属性
type UserWithoutPassword = Omit<User, "password">;

// Exclude<T, U> - 从T中排除U
type T0 = Exclude<"a" | "b" | "c", "a">; // "b" | "c"

// Extract<T, U> - 从T中提取U
type T1 = Extract<"a" | "b" | "c", "a" | "f">; // "a"

// NonNullable<T> - 排除null和undefined
type T2 = NonNullable<string | number | undefined>; // string | number

// ReturnType<T> - 获取函数返回类型
type T3 = ReturnType<() => string>; // string

// Parameters<T> - 获取函数参数类型
type T4 = Parameters<(a: string, b: number) => void>; // [string, number]
```

## 6.7 高级泛型模式

### 泛型工厂函数

```typescript
interface Constructable {
    new (...args: any[]): any;
}

function create<T extends Constructable>(ctor: T, ...args: any[]): InstanceType<T> {
    return new ctor(...args);
}

class Person {
    constructor(public name: string) {}
}

class Animal {
    constructor(public species: string) {}
}

const person = create(Person, "Alice"); // Person
const animal = create(Animal, "Dog"); // Animal
```

### 泛型装饰器

```typescript
function enumerable<T>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
): TypedPropertyDescriptor<T> | void {
    descriptor.enumerable = true;
    return descriptor;
}

class Greeter {
    greeting: string;
    
    constructor(message: string) {
        this.greeting = message;
    }
    
    @enumerable
    greet<T extends string>(name: T): string {
        return `Hello, ${name}`;
    }
}
```

## 6.8 实际应用示例

### 通用数据容器

```typescript
class Container<T> {
    private items: T[] = [];
    
    add(item: T): void {
        this.items.push(item);
    }
    
    get(index: number): T | undefined {
        return this.items[index];
    }
    
    find(predicate: (item: T) => boolean): T | undefined {
        return this.items.find(predicate);
    }
    
    filter(predicate: (item: T) => boolean): T[] {
        return this.items.filter(predicate);
    }
    
    map<U>(mapper: (item: T) => U): U[] {
        return this.items.map(mapper);
    }
    
    getAll(): T[] {
        return [...this.items];
    }
}

// 使用示例
const stringContainer = new Container<string>();
stringContainer.add("hello");
stringContainer.add("world");

const numberContainer = new Container<number>();
numberContainer.add(1);
numberContainer.add(2);
```

### 类型安全的API客户端

```typescript
interface ApiResponse<T> {
    data: T;
    status: number;
    message: string;
}

interface ApiError {
    code: string;
    message: string;
}

class ApiClient {
    private baseUrl: string;
    
    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
    }
    
    async get<T>(endpoint: string): Promise<ApiResponse<T>> {
        // 实现GET请求
        const response = await fetch(`${this.baseUrl}${endpoint}`);
        return response.json();
    }
    
    async post<T, U>(endpoint: string, data: T): Promise<ApiResponse<U>> {
        // 实现POST请求
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        return response.json();
    }
}

// 使用示例
interface User {
    id: number;
    name: string;
    email: string;
}

interface CreateUserRequest {
    name: string;
    email: string;
}

const api = new ApiClient('https://api.example.com');

// 类型安全的API调用
const users = await api.get<User[]>('/users');
const newUser = await api.post<CreateUserRequest, User>('/users', {
    name: 'Alice',
    email: '<EMAIL>'
});
```

### 状态管理器

```typescript
type StateUpdater<T> = (prevState: T) => T;

class StateManager<T> {
    private state: T;
    private listeners: Array<(state: T) => void> = [];
    
    constructor(initialState: T) {
        this.state = initialState;
    }
    
    getState(): T {
        return this.state;
    }
    
    setState(updater: T | StateUpdater<T>): void {
        if (typeof updater === 'function') {
            this.state = (updater as StateUpdater<T>)(this.state);
        } else {
            this.state = updater;
        }
        this.notifyListeners();
    }
    
    subscribe(listener: (state: T) => void): () => void {
        this.listeners.push(listener);
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }
    
    private notifyListeners(): void {
        this.listeners.forEach(listener => listener(this.state));
    }
}

// 使用示例
interface AppState {
    user: User | null;
    theme: 'light' | 'dark';
    loading: boolean;
}

const stateManager = new StateManager<AppState>({
    user: null,
    theme: 'light',
    loading: false
});

// 订阅状态变化
const unsubscribe = stateManager.subscribe(state => {
    console.log('State changed:', state);
});

// 更新状态
stateManager.setState(prevState => ({
    ...prevState,
    loading: true
}));
```

## 6.9 小结

本章我们学习了：
- 泛型的基本概念和语法
- 泛型函数、接口和类
- 泛型约束和条件类型
- 映射类型和实用工具类型
- 高级泛型模式和实际应用

下一章我们将学习高级类型。
