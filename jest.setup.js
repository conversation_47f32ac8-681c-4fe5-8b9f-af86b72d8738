// Jest测试环境设置文件

// 扩展Jest匹配器
// import 'jest-extended';

// 全局测试配置
global.console = {
  ...console,
  // 在测试中禁用console.log，但保留error和warn
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: console.warn,
  error: console.error,
};

// 设置测试超时时间
jest.setTimeout(10000);

// 模拟全局对象（如果需要）
// global.fetch = require('jest-fetch-mock');

// 测试前的全局设置
beforeAll(() => {
  // 全局测试前的设置
});

// 每个测试前的设置
beforeEach(() => {
  // 清除所有模拟
  jest.clearAllMocks();
});

// 每个测试后的清理
afterEach(() => {
  // 测试后的清理工作
});

// 所有测试后的清理
afterAll(() => {
  // 全局测试后的清理
});
