/**
 * 第4章示例：接口 (Interfaces)
 * 演示TypeScript接口的各种用法
 */

// 1. 基本接口定义
console.log("=== 基本接口定义 ===");

interface User {
    id: number;
    name: string;
    email: string;
    isActive?: boolean; // 可选属性
    readonly createdAt: Date; // 只读属性
}

const user1: User = {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    createdAt: new Date()
};

const user2: User = {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    isActive: true,
    createdAt: new Date()
};

console.log("Users:", { user1, user2 });

// user1.createdAt = new Date(); // 错误：只读属性不能修改

// 2. 函数接口
console.log("\n=== 函数接口 ===");

interface SearchFunction {
    (source: string, subString: string): boolean;
}

interface MathOperation {
    (a: number, b: number): number;
}

const search: SearchFunction = (source, subString) => {
    return source.indexOf(subString) > -1;
};

const add: MathOperation = (a, b) => a + b;
const multiply: MathOperation = (a, b) => a * b;

console.log("Function interfaces:", {
    search: search("Hello World", "World"),
    add: add(10, 5),
    multiply: multiply(10, 5)
});

// 3. 对象方法接口
console.log("\n=== 对象方法接口 ===");

interface Calculator {
    add(a: number, b: number): number;
    subtract(a: number, b: number): number;
    multiply(a: number, b: number): number;
    divide(a: number, b: number): number;
}

const calculator: Calculator = {
    add: (a, b) => a + b,
    subtract: (a, b) => a - b,
    multiply: (a, b) => a * b,
    divide: (a, b) => {
        if (b === 0) throw new Error("Division by zero");
        return a / b;
    }
};

console.log("Calculator:", {
    add: calculator.add(10, 5),
    subtract: calculator.subtract(10, 5),
    multiply: calculator.multiply(10, 5),
    divide: calculator.divide(10, 5)
});

// 4. 索引签名
console.log("\n=== 索引签名 ===");

interface StringDictionary {
    [key: string]: string;
}

interface NumberDictionary {
    [key: string]: number;
}

interface MixedDictionary {
    [key: string]: string | number;
    length: number; // 具体属性必须符合索引签名
}

const colors: StringDictionary = {
    red: "#FF0000",
    green: "#00FF00",
    blue: "#0000FF"
};

const scores: NumberDictionary = {
    math: 95,
    science: 87,
    english: 92
};

const mixed: MixedDictionary = {
    name: "Mixed Dictionary",
    version: 1,
    length: 2
};

console.log("Dictionaries:", { colors, scores, mixed });

// 5. 接口继承
console.log("\n=== 接口继承 ===");

interface Shape {
    color: string;
    area(): number;
}

interface Stroke {
    strokeColor: string;
    strokeWidth: number;
}

interface Rectangle extends Shape, Stroke {
    width: number;
    height: number;
}

const rectangle: Rectangle = {
    color: "red",
    strokeColor: "black",
    strokeWidth: 2,
    width: 10,
    height: 20,
    area() {
        return this.width * this.height;
    }
};

console.log("Rectangle:", {
    ...rectangle,
    area: rectangle.area()
});

// 6. 类实现接口
console.log("\n=== 类实现接口 ===");

interface Flyable {
    fly(): void;
    altitude: number;
}

interface Swimmable {
    swim(): void;
    depth: number;
}

class Duck implements Flyable, Swimmable {
    altitude: number = 0;
    depth: number = 0;

    fly(): void {
        this.altitude = 100;
        console.log(`Duck is flying at altitude ${this.altitude}m`);
    }

    swim(): void {
        this.depth = 5;
        console.log(`Duck is swimming at depth ${this.depth}m`);
    }

    quack(): void {
        console.log("Quack! Quack!");
    }
}

const duck = new Duck();
duck.fly();
duck.swim();
duck.quack();

// 7. 泛型接口
console.log("\n=== 泛型接口 ===");

interface Container<T> {
    value: T;
    getValue(): T;
    setValue(value: T): void;
}

interface Pair<T, U> {
    first: T;
    second: U;
}

class Box<T> implements Container<T> {
    constructor(public value: T) {}

    getValue(): T {
        return this.value;
    }

    setValue(value: T): void {
        this.value = value;
    }
}

const stringBox = new Box<string>("Hello");
const numberBox = new Box<number>(42);

const pair: Pair<string, number> = {
    first: "Answer",
    second: 42
};

console.log("Generic interfaces:", {
    stringBox: stringBox.getValue(),
    numberBox: numberBox.getValue(),
    pair
});

// 8. 混合类型接口
console.log("\n=== 混合类型接口 ===");

interface Counter {
    (start: number): string;
    interval: number;
    reset(): void;
    count: number;
}

function getCounter(): Counter {
    let counter = function(start: number) {
        counter.count = start;
        return `Counter started at ${start}`;
    } as Counter;

    counter.interval = 1000;
    counter.count = 0;
    counter.reset = function() {
        this.count = 0;
        console.log("Counter reset");
    };

    return counter;
}

const myCounter = getCounter();
console.log("Mixed type:", {
    call: myCounter(10),
    interval: myCounter.interval,
    count: myCounter.count
});
myCounter.reset();

// 9. API 接口示例
console.log("\n=== API 接口示例 ===");

interface ApiResponse<T> {
    data: T;
    status: number;
    message: string;
    timestamp: Date;
}

interface UserData {
    id: number;
    username: string;
    profile: {
        firstName: string;
        lastName: string;
        avatar?: string;
    };
}

interface ApiError {
    code: string;
    message: string;
    details?: any;
}

// 模拟API响应
const userResponse: ApiResponse<UserData> = {
    data: {
        id: 1,
        username: "alice_j",
        profile: {
            firstName: "Alice",
            lastName: "Johnson"
        }
    },
    status: 200,
    message: "Success",
    timestamp: new Date()
};

const errorResponse: ApiResponse<ApiError> = {
    data: {
        code: "USER_NOT_FOUND",
        message: "User with specified ID was not found"
    },
    status: 404,
    message: "Not Found",
    timestamp: new Date()
};

console.log("API responses:", { userResponse, errorResponse });

export {
    User,
    SearchFunction,
    MathOperation,
    Calculator,
    StringDictionary,
    NumberDictionary,
    Shape,
    Rectangle,
    Flyable,
    Swimmable,
    Duck,
    Container,
    Pair,
    Box,
    Counter,
    ApiResponse,
    UserData,
    ApiError,
    getCounter
};
