# 第10章：综合实战项目 - 任务管理系统

## 10.1 项目概述

在这个综合实战项目中，我们将构建一个完整的任务管理系统，整合前面学到的所有TypeScript知识点。

### 项目特性

- **类型安全**：全程使用TypeScript严格模式
- **模块化设计**：清晰的代码组织结构
- **数据持久化**：本地存储支持
- **事件系统**：观察者模式实现
- **错误处理**：完善的异常处理机制
- **测试覆盖**：单元测试和集成测试

### 技术栈

- TypeScript 5.0+
- Node.js (运行环境)
- Jest (测试框架)
- 本地文件系统 (数据存储)

## 10.2 项目架构

```
src/chapter10/
├── models/          # 数据模型
│   ├── Task.ts
│   ├── User.ts
│   └── Project.ts
├── services/        # 业务逻辑服务
│   ├── TaskService.ts
│   ├── UserService.ts
│   └── ProjectService.ts
├── repositories/    # 数据访问层
│   ├── BaseRepository.ts
│   ├── TaskRepository.ts
│   └── FileStorage.ts
├── utils/          # 工具函数
│   ├── Logger.ts
│   ├── Validator.ts
│   └── DateUtils.ts
├── types/          # 类型定义
│   ├── common.ts
│   ├── api.ts
│   └── events.ts
├── events/         # 事件系统
│   ├── EventEmitter.ts
│   └── TaskEvents.ts
├── cli/            # 命令行界面
│   ├── CLI.ts
│   └── Commands.ts
└── app.ts          # 应用入口
```

## 10.3 核心功能

### 任务管理
- 创建、编辑、删除任务
- 任务状态管理（待办、进行中、已完成）
- 任务优先级设置
- 任务分类和标签
- 截止日期管理

### 项目管理
- 项目创建和管理
- 任务分组
- 项目进度跟踪

### 用户系统
- 用户创建和管理
- 任务分配
- 权限控制

### 数据持久化
- JSON文件存储
- 数据备份和恢复
- 数据验证

## 10.4 设计模式应用

### 1. 仓储模式 (Repository Pattern)
```typescript
interface IRepository<T> {
    findById(id: string): Promise<T | null>;
    findAll(): Promise<T[]>;
    create(entity: T): Promise<T>;
    update(id: string, entity: Partial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
}
```

### 2. 服务层模式 (Service Layer Pattern)
```typescript
class TaskService {
    constructor(
        private taskRepository: ITaskRepository,
        private eventEmitter: IEventEmitter
    ) {}
    
    async createTask(taskData: CreateTaskDTO): Promise<Task> {
        // 业务逻辑处理
    }
}
```

### 3. 观察者模式 (Observer Pattern)
```typescript
interface IEventEmitter {
    on<T>(event: string, listener: (data: T) => void): void;
    emit<T>(event: string, data: T): void;
    off<T>(event: string, listener: (data: T) => void): void;
}
```

### 4. 工厂模式 (Factory Pattern)
```typescript
class TaskFactory {
    static createTask(type: TaskType, data: any): Task {
        switch (type) {
            case TaskType.SIMPLE:
                return new SimpleTask(data);
            case TaskType.RECURRING:
                return new RecurringTask(data);
            default:
                throw new Error(`Unknown task type: ${type}`);
        }
    }
}
```

## 10.5 类型系统设计

### 基础类型定义
```typescript
// 任务状态枚举
enum TaskStatus {
    TODO = "todo",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}

// 优先级枚举
enum Priority {
    LOW = 1,
    MEDIUM = 2,
    HIGH = 3,
    CRITICAL = 4
}

// 任务类型
interface Task {
    id: string;
    title: string;
    description?: string;
    status: TaskStatus;
    priority: Priority;
    createdAt: Date;
    updatedAt: Date;
    dueDate?: Date;
    assigneeId?: string;
    projectId?: string;
    tags: string[];
}
```

### 高级类型应用
```typescript
// 条件类型
type TaskUpdate<T extends keyof Task> = Pick<Task, T> & {
    updatedAt: Date;
};

// 映射类型
type TaskFilters = {
    [K in keyof Task]?: Task[K] extends string 
        ? string | RegExp 
        : Task[K] extends Date 
            ? DateRange 
            : Task[K];
};

// 工具类型
type CreateTaskDTO = Omit<Task, 'id' | 'createdAt' | 'updatedAt'>;
type UpdateTaskDTO = Partial<CreateTaskDTO>;
```

## 10.6 错误处理策略

### 自定义错误类型
```typescript
abstract class AppError extends Error {
    abstract readonly statusCode: number;
    abstract readonly isOperational: boolean;
    
    constructor(message: string, public readonly context?: any) {
        super(message);
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}

class ValidationError extends AppError {
    readonly statusCode = 400;
    readonly isOperational = true;
}

class NotFoundError extends AppError {
    readonly statusCode = 404;
    readonly isOperational = true;
}
```

### 结果类型模式
```typescript
type Result<T, E = Error> = 
    | { success: true; data: T }
    | { success: false; error: E };

class TaskService {
    async createTask(data: CreateTaskDTO): Promise<Result<Task, ValidationError>> {
        try {
            const task = await this.taskRepository.create(data);
            return { success: true, data: task };
        } catch (error) {
            return { success: false, error: error as ValidationError };
        }
    }
}
```

## 10.7 测试策略

### 单元测试
```typescript
describe('TaskService', () => {
    let taskService: TaskService;
    let mockRepository: jest.Mocked<ITaskRepository>;
    
    beforeEach(() => {
        mockRepository = createMockRepository();
        taskService = new TaskService(mockRepository);
    });
    
    it('should create a task successfully', async () => {
        const taskData: CreateTaskDTO = {
            title: 'Test Task',
            status: TaskStatus.TODO,
            priority: Priority.MEDIUM,
            tags: []
        };
        
        const result = await taskService.createTask(taskData);
        
        expect(result.success).toBe(true);
        if (result.success) {
            expect(result.data.title).toBe(taskData.title);
        }
    });
});
```

### 集成测试
```typescript
describe('Task Management Integration', () => {
    let app: TaskManagementApp;
    
    beforeEach(async () => {
        app = new TaskManagementApp();
        await app.initialize();
    });
    
    it('should handle complete task workflow', async () => {
        // 创建任务
        const task = await app.createTask({
            title: 'Integration Test Task',
            status: TaskStatus.TODO,
            priority: Priority.HIGH,
            tags: ['test']
        });
        
        // 更新任务状态
        await app.updateTaskStatus(task.id, TaskStatus.IN_PROGRESS);
        
        // 完成任务
        await app.completeTask(task.id);
        
        // 验证最终状态
        const completedTask = await app.getTask(task.id);
        expect(completedTask?.status).toBe(TaskStatus.COMPLETED);
    });
});
```

## 10.8 性能优化

### 缓存策略
```typescript
class CachedTaskRepository implements ITaskRepository {
    private cache = new Map<string, Task>();
    
    constructor(private baseRepository: ITaskRepository) {}
    
    async findById(id: string): Promise<Task | null> {
        if (this.cache.has(id)) {
            return this.cache.get(id)!;
        }
        
        const task = await this.baseRepository.findById(id);
        if (task) {
            this.cache.set(id, task);
        }
        
        return task;
    }
}
```

### 批量操作
```typescript
class BatchTaskService {
    async createTasks(tasks: CreateTaskDTO[]): Promise<Result<Task[], ValidationError[]>> {
        const results = await Promise.allSettled(
            tasks.map(task => this.taskService.createTask(task))
        );
        
        const successful: Task[] = [];
        const errors: ValidationError[] = [];
        
        results.forEach((result, index) => {
            if (result.status === 'fulfilled' && result.value.success) {
                successful.push(result.value.data);
            } else if (result.status === 'fulfilled' && !result.value.success) {
                errors.push(result.value.error);
            }
        });
        
        return successful.length > 0 
            ? { success: true, data: successful }
            : { success: false, error: errors };
    }
}
```

## 10.9 部署和构建

### 构建脚本
```json
{
  "scripts": {
    "build": "tsc",
    "build:prod": "tsc --project tsconfig.prod.json",
    "start": "node dist/chapter10/app.js",
    "dev": "ts-node src/chapter10/app.ts",
    "test": "jest src/chapter10",
    "test:coverage": "jest src/chapter10 --coverage"
  }
}
```

### 环境配置
```typescript
interface AppConfig {
    environment: 'development' | 'production' | 'test';
    dataPath: string;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableCache: boolean;
}

class ConfigManager {
    static load(): AppConfig {
        return {
            environment: (process.env.NODE_ENV as any) || 'development',
            dataPath: process.env.DATA_PATH || './data',
            logLevel: (process.env.LOG_LEVEL as any) || 'info',
            enableCache: process.env.ENABLE_CACHE === 'true'
        };
    }
}
```

## 10.10 项目总结

这个实战项目展示了：

1. **类型安全**：全面使用TypeScript类型系统
2. **架构设计**：清晰的分层架构和模块化设计
3. **设计模式**：实际应用多种设计模式
4. **错误处理**：完善的错误处理机制
5. **测试覆盖**：单元测试和集成测试
6. **性能优化**：缓存和批量操作
7. **工程化**：构建、部署和配置管理

通过这个项目，您可以：
- 理解大型TypeScript项目的组织方式
- 掌握类型系统在实际项目中的应用
- 学习最佳实践和设计模式
- 获得完整的项目开发经验

## 10.11 扩展建议

1. **Web界面**：添加React/Vue前端界面
2. **数据库集成**：替换文件存储为数据库
3. **API服务**：构建RESTful API
4. **实时同步**：WebSocket实时更新
5. **移动应用**：React Native移动端
6. **微服务**：拆分为微服务架构

继续学习和实践，您将成为TypeScript专家！
