/**
 * TypeScript 从入门到精通教程 - 主入口文件
 * 
 * 这个文件演示了如何运行各个章节的示例代码
 * 运行方式：npm run dev 或 ts-node src/index.ts
 */

console.log("🚀 TypeScript 从入门到精通教程");
console.log("=====================================\n");

// 导入各章节示例
import * as Chapter01 from "./chapter01/01-hello-world";
import * as Chapter02 from "./chapter02/01-basic-types";
import * as Chapter04 from "./chapter04/01-interfaces";

console.log("📚 教程章节概览：");
console.log("第1章：TypeScript简介与环境搭建");
console.log("第2章：基础类型系统");
console.log("第3章：变量声明与函数");
console.log("第4章：接口 (Interfaces)");
console.log("第5章：类 (Classes)");
console.log("第6章：泛型 (Generics)");
console.log("第7章：高级类型");
console.log("第8章：装饰器 (Decorators)");
console.log("第9章：模块系统");
console.log("第10章：综合实战项目\n");

// 演示第1章内容
console.log("🎯 第1章示例：");
console.log(Chapter01.sayHello("TypeScript学习者"));
console.log(Chapter01.greetUser("Alice", "欢迎"));

// 演示第2章内容
console.log("\n🎯 第2章示例：");
console.log("颜色枚举值：", Chapter02.Color.Blue);
console.log("方向枚举值：", Chapter02.Direction.Up);

// 演示第4章内容
console.log("\n🎯 第4章示例：");
const duck = new Chapter04.Duck();
duck.fly();
duck.swim();

console.log("\n✅ 教程示例运行完成！");
console.log("\n📖 学习建议：");
console.log("1. 按章节顺序学习，每章都有详细的README.md");
console.log("2. 运行每个章节的示例代码：ts-node src/chapterXX/xx-example.ts");
console.log("3. 完成exercises目录下的练习题");
console.log("4. 查看solutions目录下的参考答案");
console.log("\n🛠️ 开发工具推荐：");
console.log("- VS Code + TypeScript扩展");
console.log("- 启用严格模式进行类型检查");
console.log("- 使用Prettier格式化代码");
console.log("\n🎉 祝您学习愉快！");

export default {
    message: "TypeScript Tutorial Main Entry Point"
};
