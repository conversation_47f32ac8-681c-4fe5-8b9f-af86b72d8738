/**
 * 第1章示例：Hello World
 * 演示TypeScript的基本语法和类型注解
 */

// 1. 基本的函数定义和类型注解
function sayHello(name: string): string {
    return `Hello, ${name}!`;
}

// 2. 变量类型注解
const userName: string = "TypeScript";
const userAge: number = 25;
const isStudent: boolean = true;

// 3. 调用函数
console.log(sayHello(userName));
console.log(`Age: ${userAge}, Is Student: ${isStudent}`);

// 4. 类型错误示例（注释掉避免编译错误）
// sayHello(123); // 错误：Argument of type 'number' is not assignable to parameter of type 'string'
// const wrongType: string = 123; // 错误：Type 'number' is not assignable to type 'string'

// 5. 数组类型
const numbers: number[] = [1, 2, 3, 4, 5];
const names: string[] = ["<PERSON>", "<PERSON>", "<PERSON>"];

console.log("Numbers:", numbers);
console.log("Names:", names);

// 6. 对象类型（简单版本）
const user: { name: string; age: number; isActive: boolean } = {
    name: "<PERSON>",
    age: 30,
    isActive: true
};

console.log("User:", user);

// 7. 函数参数可选
function greetUser(name: string, greeting?: string): string {
    const defaultGreeting = greeting || "Hello";
    return `${defaultGreeting}, ${name}!`;
}

console.log(greetUser("Alice"));
console.log(greetUser("Bob", "Hi"));

// 8. 函数返回值类型推断
function addNumbers(a: number, b: number) {
    return a + b; // TypeScript自动推断返回类型为number
}

const sum = addNumbers(10, 20);
console.log("Sum:", sum);

// 9. 联合类型简介
function printId(id: string | number): void {
    console.log("ID:", id);
}

printId("abc123");
printId(456);

// 10. 类型断言简介
const someValue: unknown = "Hello TypeScript";
const strLength: number = (someValue as string).length;
console.log("String length:", strLength);

export {
    sayHello,
    greetUser,
    addNumbers,
    printId
};
