/**
 * 任务模型类
 * 实现任务的业务逻辑和验证
 */

import { Task as ITask, TaskStatus, Priority, CreateTaskDTO, UpdateTaskDTO } from "../types/common";
import { ValidationError } from "../utils/errors";
import { generateId } from "../utils/helpers";

export class Task implements ITask {
    public readonly id: string;
    public title: string;
    public description?: string;
    public status: TaskStatus;
    public priority: Priority;
    public readonly createdAt: Date;
    public updatedAt: Date;
    public dueDate?: Date;
    public assigneeId?: string;
    public projectId?: string;
    public tags: string[];
    public estimatedHours?: number;
    public actualHours?: number;

    constructor(data: CreateTaskDTO & { id?: string; createdAt?: Date; updatedAt?: Date }) {
        this.id = data.id || generateId();
        this.title = data.title;
        this.description = data.description;
        this.status = data.status;
        this.priority = data.priority;
        this.createdAt = data.createdAt || new Date();
        this.updatedAt = data.updatedAt || new Date();
        this.dueDate = data.dueDate;
        this.assigneeId = data.assigneeId;
        this.projectId = data.projectId;
        this.tags = [...(data.tags || [])];
        this.estimatedHours = data.estimatedHours;
        this.actualHours = data.actualHours;

        this.validate();
    }

    /**
     * 验证任务数据
     */
    private validate(): void {
        if (!this.title || this.title.trim().length === 0) {
            throw new ValidationError("任务标题不能为空");
        }

        if (this.title.length > 200) {
            throw new ValidationError("任务标题不能超过200个字符");
        }

        if (this.description && this.description.length > 2000) {
            throw new ValidationError("任务描述不能超过2000个字符");
        }

        if (!Object.values(TaskStatus).includes(this.status)) {
            throw new ValidationError(`无效的任务状态: ${this.status}`);
        }

        if (!Object.values(Priority).includes(this.priority)) {
            throw new ValidationError(`无效的优先级: ${this.priority}`);
        }

        if (this.dueDate && this.dueDate < this.createdAt) {
            throw new ValidationError("截止日期不能早于创建日期");
        }

        if (this.estimatedHours !== undefined && this.estimatedHours < 0) {
            throw new ValidationError("预估工时不能为负数");
        }

        if (this.actualHours !== undefined && this.actualHours < 0) {
            throw new ValidationError("实际工时不能为负数");
        }

        // 验证标签
        if (this.tags.some(tag => !tag || tag.trim().length === 0)) {
            throw new ValidationError("标签不能为空");
        }

        if (this.tags.some(tag => tag.length > 50)) {
            throw new ValidationError("标签长度不能超过50个字符");
        }
    }

    /**
     * 更新任务
     */
    public update(data: UpdateTaskDTO): void {
        const oldData = this.toJSON();

        // 更新字段
        if (data.title !== undefined) this.title = data.title;
        if (data.description !== undefined) this.description = data.description;
        if (data.status !== undefined) this.status = data.status;
        if (data.priority !== undefined) this.priority = data.priority;
        if (data.dueDate !== undefined) this.dueDate = data.dueDate;
        if (data.assigneeId !== undefined) this.assigneeId = data.assigneeId;
        if (data.projectId !== undefined) this.projectId = data.projectId;
        if (data.tags !== undefined) this.tags = [...data.tags];
        if (data.estimatedHours !== undefined) this.estimatedHours = data.estimatedHours;
        if (data.actualHours !== undefined) this.actualHours = data.actualHours;

        this.updatedAt = new Date();

        try {
            this.validate();
        } catch (error) {
            // 恢复原始数据
            Object.assign(this, oldData);
            throw error;
        }
    }

    /**
     * 更改任务状态
     */
    public changeStatus(newStatus: TaskStatus): void {
        if (this.status === newStatus) {
            return;
        }

        // 验证状态转换
        if (!this.isValidStatusTransition(this.status, newStatus)) {
            throw new ValidationError(`无效的状态转换: ${this.status} -> ${newStatus}`);
        }

        this.status = newStatus;
        this.updatedAt = new Date();

        // 如果任务完成，记录实际完成时间
        if (newStatus === TaskStatus.COMPLETED && !this.actualHours) {
            // 这里可以根据业务逻辑设置实际工时
        }
    }

    /**
     * 验证状态转换是否有效
     */
    private isValidStatusTransition(from: TaskStatus, to: TaskStatus): boolean {
        const validTransitions: Record<TaskStatus, TaskStatus[]> = {
            [TaskStatus.TODO]: [TaskStatus.IN_PROGRESS, TaskStatus.CANCELLED],
            [TaskStatus.IN_PROGRESS]: [TaskStatus.TODO, TaskStatus.COMPLETED, TaskStatus.CANCELLED],
            [TaskStatus.COMPLETED]: [TaskStatus.IN_PROGRESS], // 允许重新打开已完成的任务
            [TaskStatus.CANCELLED]: [TaskStatus.TODO, TaskStatus.IN_PROGRESS]
        };

        return validTransitions[from]?.includes(to) || false;
    }

    /**
     * 添加标签
     */
    public addTag(tag: string): void {
        const trimmedTag = tag.trim();
        if (!trimmedTag) {
            throw new ValidationError("标签不能为空");
        }

        if (trimmedTag.length > 50) {
            throw new ValidationError("标签长度不能超过50个字符");
        }

        if (!this.tags.includes(trimmedTag)) {
            this.tags.push(trimmedTag);
            this.updatedAt = new Date();
        }
    }

    /**
     * 移除标签
     */
    public removeTag(tag: string): void {
        const index = this.tags.indexOf(tag);
        if (index > -1) {
            this.tags.splice(index, 1);
            this.updatedAt = new Date();
        }
    }

    /**
     * 检查任务是否逾期
     */
    public isOverdue(): boolean {
        if (!this.dueDate) {
            return false;
        }

        return this.dueDate < new Date() && this.status !== TaskStatus.COMPLETED;
    }

    /**
     * 获取任务进度百分比
     */
    public getProgress(): number {
        switch (this.status) {
            case TaskStatus.TODO:
                return 0;
            case TaskStatus.IN_PROGRESS:
                return 50;
            case TaskStatus.COMPLETED:
                return 100;
            case TaskStatus.CANCELLED:
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 计算剩余天数
     */
    public getDaysRemaining(): number | null {
        if (!this.dueDate) {
            return null;
        }

        const now = new Date();
        const diffTime = this.dueDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays;
    }

    /**
     * 获取优先级标签
     */
    public getPriorityLabel(): string {
        const labels: Record<Priority, string> = {
            [Priority.LOW]: "低",
            [Priority.MEDIUM]: "中",
            [Priority.HIGH]: "高",
            [Priority.CRITICAL]: "紧急"
        };

        return labels[this.priority];
    }

    /**
     * 获取状态标签
     */
    public getStatusLabel(): string {
        const labels: Record<TaskStatus, string> = {
            [TaskStatus.TODO]: "待办",
            [TaskStatus.IN_PROGRESS]: "进行中",
            [TaskStatus.COMPLETED]: "已完成",
            [TaskStatus.CANCELLED]: "已取消"
        };

        return labels[this.status];
    }

    /**
     * 克隆任务
     */
    public clone(): Task {
        return new Task({
            title: `${this.title} (副本)`,
            description: this.description,
            status: TaskStatus.TODO,
            priority: this.priority,
            dueDate: this.dueDate,
            assigneeId: this.assigneeId,
            projectId: this.projectId,
            tags: [...this.tags],
            estimatedHours: this.estimatedHours
        });
    }

    /**
     * 转换为JSON对象
     */
    public toJSON(): ITask {
        return {
            id: this.id,
            title: this.title,
            description: this.description,
            status: this.status,
            priority: this.priority,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            dueDate: this.dueDate,
            assigneeId: this.assigneeId,
            projectId: this.projectId,
            tags: [...this.tags],
            estimatedHours: this.estimatedHours,
            actualHours: this.actualHours
        };
    }

    /**
     * 从JSON对象创建任务实例
     */
    public static fromJSON(data: ITask): Task {
        return new Task({
            ...data,
            createdAt: new Date(data.createdAt),
            updatedAt: new Date(data.updatedAt),
            dueDate: data.dueDate ? new Date(data.dueDate) : undefined
        });
    }

    /**
     * 比较两个任务是否相等
     */
    public equals(other: Task): boolean {
        return this.id === other.id;
    }

    /**
     * 获取任务摘要信息
     */
    public getSummary(): string {
        const status = this.getStatusLabel();
        const priority = this.getPriorityLabel();
        const dueInfo = this.dueDate ? ` (截止: ${this.dueDate.toLocaleDateString()})` : "";
        
        return `[${priority}] ${this.title} - ${status}${dueInfo}`;
    }
}
