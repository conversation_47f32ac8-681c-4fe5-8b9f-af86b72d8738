# 第4章：接口 (Interfaces)

## 4.1 接口基础

接口是TypeScript的核心特性之一，用于定义对象的结构和契约。

### 基本接口定义

```typescript
interface User {
    id: number;
    name: string;
    email: string;
}

const user: User = {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>"
};
```

### 可选属性

```typescript
interface Config {
    host: string;
    port?: number; // 可选属性
    ssl?: boolean;
}

const config1: Config = { host: "localhost" }; // OK
const config2: Config = { host: "localhost", port: 3000 }; // OK
```

### 只读属性

```typescript
interface Point {
    readonly x: number;
    readonly y: number;
}

const point: Point = { x: 10, y: 20 };
// point.x = 5; // 错误：Cannot assign to 'x' because it is a read-only property
```

## 4.2 函数接口

### 函数类型接口

```typescript
interface SearchFunc {
    (source: string, subString: string): boolean;
}

const mySearch: SearchFunc = function(source, subString) {
    return source.search(subString) > -1;
};
```

### 方法签名

```typescript
interface Calculator {
    add(a: number, b: number): number;
    subtract(a: number, b: number): number;
}

const calc: Calculator = {
    add: (a, b) => a + b,
    subtract: (a, b) => a - b
};
```

## 4.3 索引签名

### 字符串索引签名

```typescript
interface StringDictionary {
    [key: string]: string;
}

const colors: StringDictionary = {
    red: "#FF0000",
    green: "#00FF00",
    blue: "#0000FF"
};
```

### 数字索引签名

```typescript
interface NumberArray {
    [index: number]: string;
}

const fruits: NumberArray = ["apple", "banana", "orange"];
```

### 混合索引签名

```typescript
interface MixedDictionary {
    [key: string]: string | number;
    length: number; // 具体属性
}
```

## 4.4 类接口

### 类实现接口

```typescript
interface Flyable {
    fly(): void;
}

interface Swimmable {
    swim(): void;
}

class Duck implements Flyable, Swimmable {
    fly(): void {
        console.log("Duck is flying");
    }
    
    swim(): void {
        console.log("Duck is swimming");
    }
}
```

### 构造器签名

```typescript
interface ClockConstructor {
    new (hour: number, minute: number): ClockInterface;
}

interface ClockInterface {
    tick(): void;
}

class DigitalClock implements ClockInterface {
    constructor(h: number, m: number) {}
    tick() {
        console.log("beep beep");
    }
}

function createClock(
    ctor: ClockConstructor,
    hour: number,
    minute: number
): ClockInterface {
    return new ctor(hour, minute);
}
```

## 4.5 接口继承

### 单继承

```typescript
interface Shape {
    color: string;
}

interface Square extends Shape {
    sideLength: number;
}

const square: Square = {
    color: "red",
    sideLength: 10
};
```

### 多继承

```typescript
interface Shape {
    color: string;
}

interface PenStroke {
    penWidth: number;
}

interface Square extends Shape, PenStroke {
    sideLength: number;
}
```

## 4.6 混合类型

```typescript
interface Counter {
    (start: number): string;
    interval: number;
    reset(): void;
}

function getCounter(): Counter {
    let counter = function(start: number) {
        return `Started at ${start}`;
    } as Counter;
    
    counter.interval = 123;
    counter.reset = function() {
        console.log("Reset counter");
    };
    
    return counter;
}
```

## 4.7 接口与类型别名的区别

### 接口

```typescript
interface User {
    name: string;
    age: number;
}

// 接口可以声明合并
interface User {
    email: string;
}

// 现在User有name、age和email属性
```

### 类型别名

```typescript
type UserType = {
    name: string;
    age: number;
};

// 类型别名不能声明合并
// type UserType = { email: string; }; // 错误：重复标识符
```

## 4.8 泛型接口

```typescript
interface GenericIdentityFn<T> {
    (arg: T): T;
}

function identity<T>(arg: T): T {
    return arg;
}

const myIdentity: GenericIdentityFn<number> = identity;
```

## 4.9 接口的高级用法

### 条件类型接口

```typescript
interface ApiResponse<T> {
    data: T;
    status: number;
    message: string;
}

interface User {
    id: number;
    name: string;
}

type UserResponse = ApiResponse<User>;
type UsersResponse = ApiResponse<User[]>;
```

### 映射类型接口

```typescript
interface Person {
    name: string;
    age: number;
    email: string;
}

// 使所有属性可选
type PartialPerson = Partial<Person>;

// 使所有属性只读
type ReadonlyPerson = Readonly<Person>;

// 选择特定属性
type PersonName = Pick<Person, "name">;

// 排除特定属性
type PersonWithoutEmail = Omit<Person, "email">;
```

## 4.10 实际应用示例

### API 接口定义

```typescript
interface ApiConfig {
    baseURL: string;
    timeout?: number;
    headers?: Record<string, string>;
}

interface ApiResponse<T = any> {
    data: T;
    status: number;
    statusText: string;
}

interface ApiError {
    message: string;
    code: number;
    details?: any;
}

class ApiClient {
    constructor(private config: ApiConfig) {}
    
    async get<T>(url: string): Promise<ApiResponse<T>> {
        // 实现GET请求
        throw new Error("Not implemented");
    }
    
    async post<T>(url: string, data: any): Promise<ApiResponse<T>> {
        // 实现POST请求
        throw new Error("Not implemented");
    }
}
```

### 事件系统接口

```typescript
interface EventListener<T = any> {
    (event: T): void;
}

interface EventEmitter {
    on<T>(event: string, listener: EventListener<T>): void;
    off<T>(event: string, listener: EventListener<T>): void;
    emit<T>(event: string, data: T): void;
}

interface CustomEvent {
    type: string;
    timestamp: number;
    data: any;
}
```

## 4.11 小结

本章我们学习了：
- 接口的基本定义和使用
- 可选属性和只读属性
- 函数接口和索引签名
- 类实现接口
- 接口继承和混合类型
- 接口与类型别名的区别
- 泛型接口和高级用法

下一章我们将学习类 (Classes)。
