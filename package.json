{"name": "typescript-tutorial", "version": "1.0.0", "description": "TypeScript从入门到精通教程", "main": "dist/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["typescript", "tutorial", "javascript", "programming", "education"], "author": "TypeScript Tutorial", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"axios": "^1.4.0", "lodash": "^4.17.21"}, "engines": {"node": ">=16.0.0"}}