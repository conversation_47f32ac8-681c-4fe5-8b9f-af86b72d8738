{
  "compilerOptions": {
    /* 基本选项 */
    "target": "ES2020",                          /* 指定ECMAScript目标版本 */
    "module": "commonjs",                        /* 指定模块代码生成 */
    "lib": ["ES2020", "DOM"],                    /* 指定要包含在编译中的库文件 */
    "outDir": "./dist",                          /* 重定向输出目录 */
    "rootDir": "./src",                          /* 指定输入文件的根目录 */
    "removeComments": true,                      /* 删除所有注释，除了以/!*开头的版权信息 */
    
    /* 严格类型检查选项 */
    "strict": true,                              /* 启用所有严格类型检查选项 */
    "noImplicitAny": true,                       /* 在表达式和声明上有隐含的any类型时报错 */
    "strictNullChecks": true,                    /* 启用严格的null检查 */
    "strictFunctionTypes": true,                 /* 启用对函数类型的严格检查 */
    "strictBindCallApply": true,                 /* 启用对bind、call和apply方法的严格检查 */
    "strictPropertyInitialization": true,       /* 启用类属性初始化的严格检查 */
    "noImplicitReturns": true,                   /* 不是函数的所有返回路径都有返回值时报错 */
    "noFallthroughCasesInSwitch": true,         /* 报告switch语句的fallthrough错误 */
    
    /* 额外检查 */
    "noUnusedLocals": true,                      /* 有未使用的变量时报错 */
    "noUnusedParameters": true,                  /* 有未使用的参数时报错 */
    "noImplicitReturns": true,                   /* 不是函数的所有返回路径都有返回值时报错 */
    "noUncheckedIndexedAccess": true,           /* 在索引签名结果中包含undefined */
    
    /* 模块解析选项 */
    "moduleResolution": "node",                  /* 选择模块解析策略 */
    "baseUrl": "./",                             /* 用于解析非相对模块名称的基目录 */
    "paths": {                                   /* 模块名到基于baseUrl的路径映射的列表 */
      "@/*": ["src/*"],
      "@chapter*": ["src/chapter*"]
    },
    "esModuleInterop": true,                     /* 启用esModuleInterop */
    "allowSyntheticDefaultImports": true,        /* 允许从没有设置默认导出的模块中默认导入 */
    "forceConsistentCasingInFileNames": true,    /* 禁止对同一个文件的不一致的引用 */
    
    /* 实验性选项 */
    "experimentalDecorators": true,              /* 启用装饰器 */
    "emitDecoratorMetadata": true,               /* 为装饰器提供元数据支持 */
    
    /* 高级选项 */
    "skipLibCheck": true,                        /* 跳过声明文件的类型检查 */
    "resolveJsonModule": true,                   /* 支持导入.json文件 */
    "declaration": true,                         /* 生成相应的.d.ts文件 */
    "declarationMap": true,                      /* 生成声明文件的sourcemap */
    "sourceMap": true,                           /* 生成相应的.map文件 */
    "incremental": true,                         /* 启用增量编译 */
    "tsBuildInfoFile": "./dist/.tsbuildinfo"     /* 指定增量编译信息文件的路径 */
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts"
  ]
}
