/**
 * 第1章示例：基础语法练习
 * 更多TypeScript基础语法示例
 */

// 1. 不同类型的变量声明
let message: string = "Learning TypeScript";
let count: number = 42;
let isCompleted: boolean = false;
let data: any = "可以是任何类型"; // 尽量避免使用any

console.log({ message, count, isCompleted, data });

// 2. 数组的不同声明方式
const fruits: string[] = ["apple", "banana", "orange"];
const scores: Array<number> = [85, 92, 78, 96];

console.log("Fruits:", fruits);
console.log("Scores:", scores);

// 3. 元组 (Tuple) - 固定长度和类型的数组
const person: [string, number, boolean] = ["Alice", 25, true];
console.log("Person tuple:", person);

// 4. 枚举 (Enum)
enum Color {
    Red,
    Green,
    Blue
}

enum Direction {
    Up = "UP",
    Down = "DOWN",
    Left = "LEFT",
    Right = "RIGHT"
}

const favoriteColor: Color = Color.Blue;
const moveDirection: Direction = Direction.Up;

console.log("Favorite color:", favoriteColor); // 输出: 2
console.log("Move direction:", moveDirection); // 输出: "UP"

// 5. 函数重载简介
function combine(a: string, b: string): string;
function combine(a: number, b: number): number;
function combine(a: any, b: any): any {
    return a + b;
}

console.log("String combine:", combine("Hello", " World"));
console.log("Number combine:", combine(10, 20));

// 6. 箭头函数与类型
const multiply = (x: number, y: number): number => x * y;
const divide = (x: number, y: number): number => {
    if (y === 0) {
        throw new Error("Division by zero");
    }
    return x / y;
};

console.log("Multiply:", multiply(6, 7));
console.log("Divide:", divide(15, 3));

// 7. 对象类型定义
type User = {
    id: number;
    name: string;
    email?: string; // 可选属性
    readonly createdAt: Date; // 只读属性
};

const newUser: User = {
    id: 1,
    name: "John Doe",
    email: "<EMAIL>",
    createdAt: new Date()
};

console.log("New user:", newUser);
// newUser.createdAt = new Date(); // 错误：Cannot assign to 'createdAt' because it is a read-only property

// 8. 函数类型
type MathOperation = (a: number, b: number) => number;

const add: MathOperation = (a, b) => a + b;
const subtract: MathOperation = (a, b) => a - b;

console.log("Add:", add(10, 5));
console.log("Subtract:", subtract(10, 5));

// 9. 字面量类型
type Theme = "light" | "dark";
type Size = "small" | "medium" | "large";

const currentTheme: Theme = "dark";
const buttonSize: Size = "medium";

console.log("Theme:", currentTheme);
console.log("Button size:", buttonSize);

// 10. null 和 undefined
let nullableValue: string | null = null;
let undefinedValue: string | undefined = undefined;

// 类型守卫
if (nullableValue !== null) {
    console.log("Value length:", nullableValue.length);
}

// 11. 类型推断示例
let inferredString = "TypeScript"; // 自动推断为string类型
let inferredNumber = 42; // 自动推断为number类型
let inferredArray = [1, 2, 3]; // 自动推断为number[]类型

console.log("Inferred types work automatically");

// 12. 简单的错误处理
function safeDivide(a: number, b: number): number | string {
    if (b === 0) {
        return "Error: Division by zero";
    }
    return a / b;
}

console.log("Safe divide:", safeDivide(10, 2));
console.log("Safe divide by zero:", safeDivide(10, 0));

export {
    Color,
    Direction,
    combine,
    multiply,
    divide,
    User,
    MathOperation,
    Theme,
    Size,
    safeDivide
};
