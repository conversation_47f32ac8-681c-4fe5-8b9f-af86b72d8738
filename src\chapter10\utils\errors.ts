/**
 * 自定义错误类
 * 定义项目中使用的各种错误类型
 */

/**
 * 应用错误基类
 */
export abstract class AppError extends Error {
    public abstract readonly statusCode: number;
    public abstract readonly isOperational: boolean;
    
    constructor(
        message: string,
        public readonly context?: any
    ) {
        super(message);
        this.name = this.constructor.name;
        
        // 确保堆栈跟踪正确指向错误发生的位置
        Error.captureStackTrace(this, this.constructor);
    }
    
    /**
     * 获取错误的详细信息
     */
    public getDetails(): {
        name: string;
        message: string;
        statusCode: number;
        context?: any;
        stack?: string;
    } {
        return {
            name: this.name,
            message: this.message,
            statusCode: this.statusCode,
            context: this.context,
            stack: this.stack
        };
    }
}

/**
 * 验证错误
 */
export class ValidationError extends AppError {
    public readonly statusCode = 400;
    public readonly isOperational = true;
    
    constructor(message: string, context?: any) {
        super(message, context);
    }
}

/**
 * 未找到错误
 */
export class NotFoundError extends AppError {
    public readonly statusCode = 404;
    public readonly isOperational = true;
    
    constructor(resource: string, id?: string) {
        const message = id 
            ? `${resource} with id '${id}' not found`
            : `${resource} not found`;
        super(message, { resource, id });
    }
}

/**
 * 权限错误
 */
export class PermissionError extends AppError {
    public readonly statusCode = 403;
    public readonly isOperational = true;
    
    constructor(action: string, resource?: string) {
        const message = resource 
            ? `Permission denied: cannot ${action} ${resource}`
            : `Permission denied: cannot ${action}`;
        super(message, { action, resource });
    }
}

/**
 * 认证错误
 */
export class AuthenticationError extends AppError {
    public readonly statusCode = 401;
    public readonly isOperational = true;
    
    constructor(message: string = "Authentication required") {
        super(message);
    }
}

/**
 * 冲突错误
 */
export class ConflictError extends AppError {
    public readonly statusCode = 409;
    public readonly isOperational = true;
    
    constructor(message: string, context?: any) {
        super(message, context);
    }
}

/**
 * 业务逻辑错误
 */
export class BusinessLogicError extends AppError {
    public readonly statusCode = 422;
    public readonly isOperational = true;
    
    constructor(message: string, context?: any) {
        super(message, context);
    }
}

/**
 * 外部服务错误
 */
export class ExternalServiceError extends AppError {
    public readonly statusCode = 502;
    public readonly isOperational = true;
    
    constructor(service: string, message: string, context?: any) {
        super(`External service error (${service}): ${message}`, { service, ...context });
    }
}

/**
 * 数据库错误
 */
export class DatabaseError extends AppError {
    public readonly statusCode = 500;
    public readonly isOperational = false;
    
    constructor(message: string, context?: any) {
        super(`Database error: ${message}`, context);
    }
}

/**
 * 文件系统错误
 */
export class FileSystemError extends AppError {
    public readonly statusCode = 500;
    public readonly isOperational = false;
    
    constructor(operation: string, path: string, originalError?: Error) {
        super(`File system error: ${operation} failed for '${path}'`, {
            operation,
            path,
            originalError: originalError?.message
        });
    }
}

/**
 * 配置错误
 */
export class ConfigurationError extends AppError {
    public readonly statusCode = 500;
    public readonly isOperational = false;
    
    constructor(setting: string, message?: string) {
        const errorMessage = message 
            ? `Configuration error for '${setting}': ${message}`
            : `Configuration error: '${setting}' is missing or invalid`;
        super(errorMessage, { setting });
    }
}

/**
 * 网络错误
 */
export class NetworkError extends AppError {
    public readonly statusCode = 503;
    public readonly isOperational = true;
    
    constructor(message: string, context?: any) {
        super(`Network error: ${message}`, context);
    }
}

/**
 * 超时错误
 */
export class TimeoutError extends AppError {
    public readonly statusCode = 408;
    public readonly isOperational = true;
    
    constructor(operation: string, timeout: number) {
        super(`Operation '${operation}' timed out after ${timeout}ms`, {
            operation,
            timeout
        });
    }
}

/**
 * 速率限制错误
 */
export class RateLimitError extends AppError {
    public readonly statusCode = 429;
    public readonly isOperational = true;
    
    constructor(limit: number, window: number) {
        super(`Rate limit exceeded: ${limit} requests per ${window}ms`, {
            limit,
            window
        });
    }
}

/**
 * 错误工厂类
 */
export class ErrorFactory {
    /**
     * 创建验证错误
     */
    static validation(field: string, value: any, rule: string): ValidationError {
        return new ValidationError(
            `Validation failed for field '${field}': ${rule}`,
            { field, value, rule }
        );
    }
    
    /**
     * 创建必填字段错误
     */
    static required(field: string): ValidationError {
        return new ValidationError(`Field '${field}' is required`, { field });
    }
    
    /**
     * 创建类型错误
     */
    static invalidType(field: string, expected: string, actual: string): ValidationError {
        return new ValidationError(
            `Field '${field}' must be of type '${expected}', got '${actual}'`,
            { field, expected, actual }
        );
    }
    
    /**
     * 创建范围错误
     */
    static outOfRange(field: string, value: any, min?: any, max?: any): ValidationError {
        let message = `Field '${field}' value '${value}' is out of range`;
        if (min !== undefined && max !== undefined) {
            message += ` (expected: ${min} - ${max})`;
        } else if (min !== undefined) {
            message += ` (minimum: ${min})`;
        } else if (max !== undefined) {
            message += ` (maximum: ${max})`;
        }
        
        return new ValidationError(message, { field, value, min, max });
    }
    
    /**
     * 创建重复错误
     */
    static duplicate(resource: string, field: string, value: any): ConflictError {
        return new ConflictError(
            `${resource} with ${field} '${value}' already exists`,
            { resource, field, value }
        );
    }
}

/**
 * 错误处理工具类
 */
export class ErrorHandler {
    /**
     * 判断是否为操作性错误
     */
    static isOperationalError(error: Error): boolean {
        if (error instanceof AppError) {
            return error.isOperational;
        }
        return false;
    }
    
    /**
     * 获取错误状态码
     */
    static getStatusCode(error: Error): number {
        if (error instanceof AppError) {
            return error.statusCode;
        }
        return 500;
    }
    
    /**
     * 格式化错误信息
     */
    static formatError(error: Error): {
        name: string;
        message: string;
        statusCode: number;
        isOperational: boolean;
        context?: any;
        stack?: string;
    } {
        if (error instanceof AppError) {
            return {
                ...error.getDetails(),
                isOperational: error.isOperational
            };
        }
        
        return {
            name: error.name,
            message: error.message,
            statusCode: 500,
            isOperational: false,
            stack: error.stack
        };
    }
    
    /**
     * 包装未知错误
     */
    static wrapUnknownError(error: unknown, context?: any): AppError {
        if (error instanceof AppError) {
            return error;
        }
        
        if (error instanceof Error) {
            return new class extends AppError {
                readonly statusCode = 500;
                readonly isOperational = false;
            }(error.message, { originalError: error, ...context });
        }
        
        return new class extends AppError {
            readonly statusCode = 500;
            readonly isOperational = false;
        }("Unknown error occurred", { originalError: error, ...context });
    }
}

/**
 * 错误聚合器 - 收集多个错误
 */
export class ErrorAggregator {
    private errors: AppError[] = [];
    
    /**
     * 添加错误
     */
    add(error: AppError): void {
        this.errors.push(error);
    }
    
    /**
     * 检查是否有错误
     */
    hasErrors(): boolean {
        return this.errors.length > 0;
    }
    
    /**
     * 获取所有错误
     */
    getErrors(): AppError[] {
        return [...this.errors];
    }
    
    /**
     * 获取错误数量
     */
    getCount(): number {
        return this.errors.length;
    }
    
    /**
     * 清空错误
     */
    clear(): void {
        this.errors = [];
    }
    
    /**
     * 抛出聚合错误
     */
    throwIfHasErrors(): void {
        if (this.hasErrors()) {
            const messages = this.errors.map(e => e.message).join("; ");
            throw new ValidationError(`Multiple validation errors: ${messages}`, {
                errors: this.errors.map(e => e.getDetails())
            });
        }
    }
}
