# 第2章：TypeScript基础类型系统

## 2.1 基本类型

TypeScript支持JavaScript的所有基本类型，并添加了额外的类型。

### 原始类型

```typescript
// 布尔值
let isDone: boolean = false;

// 数字
let decimal: number = 6;
let hex: number = 0xf00d;
let binary: number = 0b1010;
let octal: number = 0o744;

// 字符串
let color: string = "blue";
let fullName: string = `<PERSON>`;
let sentence: string = `Hello, my name is ${fullName}`;

// 空值
let unusable: void = undefined;

// Null 和 Undefined
let u: undefined = undefined;
let n: null = null;
```

### 数组类型

```typescript
// 两种声明方式
let list1: number[] = [1, 2, 3];
let list2: Array<number> = [1, 2, 3];

// 字符串数组
let names: string[] = ["Alice", "Bob", "Charlie"];
```

### 元组类型

```typescript
// 声明一个元组类型
let x: [string, number];

// 初始化
x = ["hello", 10]; // OK

// 错误的初始化
// x = [10, "hello"]; // Error
```

## 2.2 枚举类型

### 数字枚举

```typescript
enum Color {
    Red,    // 0
    Green,  // 1
    Blue    // 2
}

let c: Color = Color.Green;
```

### 字符串枚举

```typescript
enum Direction {
    Up = "UP",
    Down = "DOWN",
    Left = "LEFT",
    Right = "RIGHT"
}
```

### 异构枚举

```typescript
enum BooleanLikeHeterogeneousEnum {
    No = 0,
    Yes = "YES"
}
```

## 2.3 Any 类型

```typescript
let notSure: any = 4;
notSure = "maybe a string instead";
notSure = false; // 也可以是个boolean

// any类型的数组
let list: any[] = [1, true, "free"];
list[1] = 100;
```

## 2.4 Unknown 类型

```typescript
let value: unknown;

value = true;             // OK
value = 42;               // OK
value = "Hello World";    // OK
value = [];               // OK
value = {};               // OK

// 使用前需要类型检查
if (typeof value === "string") {
    console.log(value.toUpperCase()); // OK
}
```

## 2.5 Never 类型

```typescript
// 返回never的函数必须存在无法达到的终点
function error(message: string): never {
    throw new Error(message);
}

// 推断的返回值类型为never
function fail() {
    return error("Something failed");
}

// 返回never的函数必须存在无法达到的终点
function infiniteLoop(): never {
    while (true) {
    }
}
```

## 2.6 Object 类型

```typescript
// object类型
let obj: object = {};
obj = { name: "Alice" };
obj = [];
obj = function() {};

// 更具体的对象类型
let person: { name: string; age: number } = {
    name: "Bob",
    age: 25
};
```

## 2.7 联合类型

```typescript
// 联合类型
let id: string | number;
id = "abc123";  // OK
id = 123;       // OK
// id = true;   // Error

function padLeft(value: string, padding: string | number) {
    if (typeof padding === "number") {
        return Array(padding + 1).join(" ") + value;
    }
    if (typeof padding === "string") {
        return padding + value;
    }
    throw new Error(`Expected string or number, got '${padding}'.`);
}
```

## 2.8 交叉类型

```typescript
interface ErrorHandling {
    success: boolean;
    error?: { message: string };
}

interface ArtworksData {
    artworks: { title: string }[];
}

interface ArtistsData {
    artists: { name: string }[];
}

// 交叉类型
type ArtworksResponse = ArtworksData & ErrorHandling;
type ArtistsResponse = ArtistsData & ErrorHandling;

const handleArtistsResponse = (response: ArtistsResponse) => {
    if (response.error) {
        console.error(response.error.message);
        return;
    }
    console.log(response.artists);
};
```

## 2.9 类型断言

```typescript
// 尖括号语法
let someValue: any = "this is a string";
let strLength: number = (<string>someValue).length;

// as语法（推荐，在JSX中只能使用这种）
let someValue2: any = "this is a string";
let strLength2: number = (someValue2 as string).length;
```

## 2.10 字面量类型

```typescript
// 字符串字面量类型
type Easing = "ease-in" | "ease-out" | "ease-in-out";

function animate(dx: number, dy: number, easing: Easing) {
    if (easing === "ease-in") {
        // ...
    } else if (easing === "ease-out") {
        // ...
    } else if (easing === "ease-in-out") {
        // ...
    }
}

animate(0, 0, "ease-in");
// animate(0, 0, "uneasy"); // Error

// 数字字面量类型
function rollDice(): 1 | 2 | 3 | 4 | 5 | 6 {
    return (Math.floor(Math.random() * 6) + 1) as 1 | 2 | 3 | 4 | 5 | 6;
}

// 布尔字面量类型
type Success = true;
type Failure = false;
```

## 2.11 小结

本章我们学习了：
- TypeScript的基本类型系统
- 原始类型、数组、元组、枚举
- any、unknown、never等特殊类型
- 联合类型和交叉类型
- 类型断言和字面量类型

下一章我们将学习变量声明和函数。
