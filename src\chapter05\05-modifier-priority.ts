/**
 * 第5章示例：属性修饰符的优先级和组合规则
 * 演示TypeScript中各种修饰符的正确使用顺序和组合方式
 */

console.log("=== TypeScript 属性修饰符优先级演示 ===\n");

// 1. 基本修饰符顺序规则
console.log("1. 基本修饰符顺序规则");
console.log("正确顺序：访问修饰符 → static → readonly");

class ModifierOrderExample {
    // ✅ 正确的修饰符顺序
    public static readonly APP_NAME: string = "TypeScript Demo";
    private static readonly VERSION: string = "1.0.0";
    protected static readonly BUILD_DATE: string = "2024-01-01";
    
    // ✅ 实例属性的正确顺序
    public readonly id: string;
    private readonly createdAt: Date;
    protected readonly category: string;
    
    // ✅ 普通属性（无readonly）
    public name: string;
    private isActive: boolean;
    protected lastModified: Date;
    
    constructor(id: string, name: string, category: string = "default") {
        this.id = id;
        this.name = name;
        this.category = category;
        this.createdAt = new Date();
        this.lastModified = new Date();
        this.isActive = true;
    }
    
    // ✅ 静态方法
    public static getAppInfo(): string {
        return `${this.APP_NAME} v${this.VERSION} (${this.BUILD_DATE})`;
    }
    
    // ✅ 实例方法
    public updateName(newName: string): void {
        this.name = newName;
        this.lastModified = new Date();
    }
}

const example = new ModifierOrderExample("001", "示例对象");
console.log("应用信息:", ModifierOrderExample.getAppInfo());
console.log("对象ID:", example.id);
console.log("对象名称:", example.name);

// 2. 构造函数参数属性的修饰符顺序
console.log("\n2. 构造函数参数属性的修饰符顺序");

class ParameterPropertyExample {
    constructor(
        // ✅ 正确的参数属性修饰符顺序
        public readonly userId: string,           // 公共只读
        private readonly hashedPassword: string, // 私有只读
        protected readonly userRole: string,     // 受保护只读
        public email: string,                    // 公共可变
        private isVerified: boolean = false,     // 私有可变（带默认值）
        protected createdAt: Date = new Date()   // 受保护可变（带默认值）
    ) {
        console.log(`用户 ${userId} 已创建，邮箱: ${email}`);
    }
    
    // 获取用户信息的公共方法
    public getUserInfo(): object {
        return {
            userId: this.userId,
            email: this.email,
            isVerified: this.isVerified,
            createdAt: this.createdAt
        };
    }
    
    // 验证用户的公共方法
    public verify(): void {
        this.isVerified = true;
        console.log(`用户 ${this.userId} 已验证`);
    }
}

const user = new ParameterPropertyExample(
    "user123",
    "hashed_password_here",
    "member",
    "<EMAIL>"
);
console.log("用户信息:", user.getUserInfo());
user.verify();

// 3. 修饰符组合的限制和规则
console.log("\n3. 修饰符组合的限制和规则");

// 抽象类示例（演示abstract修饰符的使用）
abstract class AbstractModifierExample {
    // ✅ 允许的组合
    public static readonly TYPE: string = "Abstract";
    protected static readonly MAX_INSTANCES: number = 10;
    
    // ✅ 抽象属性和方法
    protected abstract name: string;
    public abstract getId(): string;
    
    // ✅ 具体实现
    protected readonly createdAt: Date = new Date();
    
    // ✅ 具体方法
    public getCreationTime(): Date {
        return this.createdAt;
    }
    
    // ❌ 以下组合是不允许的（注释掉避免编译错误）
    // private abstract secretMethod(): void;  // 错误：private和abstract不能同时使用
    // abstract static staticAbstract(): void; // 错误：abstract和static不能同时使用
}

// 具体实现类
class ConcreteModifierExample extends AbstractModifierExample {
    protected name: string;
    
    constructor(name: string) {
        super();
        this.name = name;
    }
    
    public getId(): string {
        return `concrete_${this.name}_${Date.now()}`;
    }
    
    public getName(): string {
        return this.name;
    }
}

const concrete = new ConcreteModifierExample("示例实现");
console.log("具体类ID:", concrete.getId());
console.log("创建时间:", concrete.getCreationTime());

// 4. 实际应用场景示例
console.log("\n4. 实际应用场景示例");

class ConfigurationManager {
    // 静态只读配置常量
    public static readonly DEFAULT_TIMEOUT: number = 30000;
    private static readonly SECRET_KEY: string = "super_secret_key";
    protected static readonly API_VERSION: string = "v1";
    
    // 实例只读属性
    public readonly instanceId: string;
    private readonly encryptionKey: string;
    
    // 可变实例属性
    private isInitialized: boolean = false;
    protected lastConfigUpdate: Date;
    
    constructor(instanceId: string) {
        this.instanceId = instanceId;
        this.encryptionKey = this.generateEncryptionKey();
        this.lastConfigUpdate = new Date();
        
        console.log(`配置管理器 ${instanceId} 已初始化`);
    }
    
    // 私有方法
    private generateEncryptionKey(): string {
        return `${ConfigurationManager.SECRET_KEY}_${this.instanceId}`;
    }
    
    // 公共静态方法
    public static getDefaultConfig(): object {
        return {
            timeout: this.DEFAULT_TIMEOUT,
            apiVersion: this.API_VERSION
        };
    }
    
    // 公共实例方法
    public initialize(): void {
        if (!this.isInitialized) {
            this.isInitialized = true;
            this.lastConfigUpdate = new Date();
            console.log(`配置管理器 ${this.instanceId} 已完成初始化`);
        }
    }
    
    // 受保护方法（可被子类访问）
    protected updateConfig(): void {
        this.lastConfigUpdate = new Date();
        console.log(`配置已更新: ${this.lastConfigUpdate}`);
    }
}

const configManager = new ConfigurationManager("config_001");
console.log("默认配置:", ConfigurationManager.getDefaultConfig());
configManager.initialize();

// 5. 修饰符优先级总结
console.log("\n5. 修饰符优先级总结");
console.log("┌─────────────────────────────────────────────────────────┐");
console.log("│                   修饰符优先级表                        │");
console.log("├─────────┬─────────────┬─────────────────┬─────────────────┤");
console.log("│ 位置    │ 修饰符类型   │ 可选值          │ 说明            │");
console.log("├─────────┼─────────────┼─────────────────┼─────────────────┤");
console.log("│ 1       │ 访问修饰符   │ public/private/ │ 控制访问权限    │");
console.log("│         │             │ protected       │                 │");
console.log("├─────────┼─────────────┼─────────────────┼─────────────────┤");
console.log("│ 2       │ 静态修饰符   │ static          │ 声明静态成员    │");
console.log("├─────────┼─────────────┼─────────────────┼─────────────────┤");
console.log("│ 3       │ 只读修饰符   │ readonly        │ 声明只读属性    │");
console.log("├─────────┼─────────────┼─────────────────┼─────────────────┤");
console.log("│ 特殊    │ 抽象修饰符   │ abstract        │ 抽象类/方法     │");
console.log("└─────────┴─────────────┴─────────────────┴─────────────────┘");

console.log("\n✅ 修饰符优先级演示完成！");

// 导出示例类供其他模块使用
export {
    ModifierOrderExample,
    ParameterPropertyExample,
    AbstractModifierExample,
    ConcreteModifierExample,
    ConfigurationManager
};
