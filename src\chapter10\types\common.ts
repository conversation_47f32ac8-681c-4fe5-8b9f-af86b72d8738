/**
 * 通用类型定义
 * 定义项目中使用的基础类型和枚举
 */

// 任务状态枚举
export enum TaskStatus {
    TODO = "todo",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}

// 优先级枚举
export enum Priority {
    LOW = 1,
    MEDIUM = 2,
    HIGH = 3,
    CRITICAL = 4
}

// 用户角色枚举
export enum UserRole {
    ADMIN = "admin",
    MANAGER = "manager",
    MEMBER = "member",
    VIEWER = "viewer"
}

// 基础实体接口
export interface BaseEntity {
    id: string;
    createdAt: Date;
    updatedAt: Date;
}

// 任务接口
export interface Task extends BaseEntity {
    title: string;
    description?: string;
    status: TaskStatus;
    priority: Priority;
    dueDate?: Date;
    assigneeId?: string;
    projectId?: string;
    tags: string[];
    estimatedHours?: number;
    actualHours?: number;
}

// 用户接口
export interface User extends BaseEntity {
    username: string;
    email: string;
    fullName: string;
    role: UserRole;
    isActive: boolean;
    lastLoginAt?: Date;
}

// 项目接口
export interface Project extends BaseEntity {
    name: string;
    description?: string;
    ownerId: string;
    memberIds: string[];
    isActive: boolean;
    startDate?: Date;
    endDate?: Date;
}

// 日期范围类型
export interface DateRange {
    start: Date;
    end: Date;
}

// 分页参数
export interface PaginationParams {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
}

// 分页结果
export interface PaginatedResult<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

// 查询过滤器
export interface TaskFilters {
    status?: TaskStatus[];
    priority?: Priority[];
    assigneeId?: string;
    projectId?: string;
    tags?: string[];
    dueDateRange?: DateRange;
    createdDateRange?: DateRange;
    search?: string;
}

// 用户过滤器
export interface UserFilters {
    role?: UserRole[];
    isActive?: boolean;
    search?: string;
}

// 项目过滤器
export interface ProjectFilters {
    ownerId?: string;
    isActive?: boolean;
    search?: string;
}

// 创建任务DTO
export type CreateTaskDTO = Omit<Task, "id" | "createdAt" | "updatedAt">;

// 更新任务DTO
export type UpdateTaskDTO = Partial<CreateTaskDTO>;

// 创建用户DTO
export type CreateUserDTO = Omit<User, "id" | "createdAt" | "updatedAt" | "lastLoginAt">;

// 更新用户DTO
export type UpdateUserDTO = Partial<Omit<CreateUserDTO, "username">>;

// 创建项目DTO
export type CreateProjectDTO = Omit<Project, "id" | "createdAt" | "updatedAt">;

// 更新项目DTO
export type UpdateProjectDTO = Partial<CreateProjectDTO>;

// 结果类型
export type Result<T, E = Error> = 
    | { success: true; data: T }
    | { success: false; error: E };

// 异步结果类型
export type AsyncResult<T, E = Error> = Promise<Result<T, E>>;

// 验证错误详情
export interface ValidationErrorDetail {
    field: string;
    message: string;
    value?: any;
}

// API响应类型
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: {
        code: string;
        message: string;
        details?: ValidationErrorDetail[];
    };
    timestamp: Date;
}

// 事件类型
export interface TaskEvent {
    type: "task_created" | "task_updated" | "task_deleted" | "task_status_changed";
    taskId: string;
    userId?: string;
    timestamp: Date;
    data?: any;
}

export interface UserEvent {
    type: "user_created" | "user_updated" | "user_deleted" | "user_login";
    userId: string;
    timestamp: Date;
    data?: any;
}

export interface ProjectEvent {
    type: "project_created" | "project_updated" | "project_deleted" | "member_added" | "member_removed";
    projectId: string;
    userId?: string;
    timestamp: Date;
    data?: any;
}

// 统计信息类型
export interface TaskStatistics {
    total: number;
    byStatus: Record<TaskStatus, number>;
    byPriority: Record<Priority, number>;
    overdue: number;
    completedThisWeek: number;
    completedThisMonth: number;
}

export interface UserStatistics {
    total: number;
    active: number;
    byRole: Record<UserRole, number>;
    recentLogins: number;
}

export interface ProjectStatistics {
    total: number;
    active: number;
    tasksTotal: number;
    tasksCompleted: number;
    averageCompletionRate: number;
}

// 配置类型
export interface AppConfig {
    environment: "development" | "production" | "test";
    dataPath: string;
    logLevel: "debug" | "info" | "warn" | "error";
    enableCache: boolean;
    cacheSize: number;
    backupInterval: number; // 分钟
    maxBackups: number;
}

// 日志级别
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

// 日志条目
export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    context?: any;
    error?: Error;
}

// 缓存条目
export interface CacheEntry<T> {
    key: string;
    value: T;
    expiresAt: Date;
    accessCount: number;
    lastAccessed: Date;
}

// 备份信息
export interface BackupInfo {
    filename: string;
    timestamp: Date;
    size: number;
    checksum: string;
}

// 导出所有类型的联合类型，便于类型检查
export type AllEntities = Task | User | Project;
export type AllEvents = TaskEvent | UserEvent | ProjectEvent;
export type AllFilters = TaskFilters | UserFilters | ProjectFilters;
export type AllCreateDTOs = CreateTaskDTO | CreateUserDTO | CreateProjectDTO;
export type AllUpdateDTOs = UpdateTaskDTO | UpdateUserDTO | UpdateProjectDTO;
