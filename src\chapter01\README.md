# 第1章：TypeScript简介与环境搭建

## 1.1 什么是TypeScript？

TypeScript是由Microsoft开发的开源编程语言，它是JavaScript的超集，添加了静态类型定义。

### 主要特点：
- **静态类型检查**：在编译时发现错误
- **现代JavaScript特性**：支持ES6+语法
- **强大的IDE支持**：智能提示、重构等
- **渐进式采用**：可以逐步迁移现有JavaScript项目

### TypeScript vs JavaScript

```typescript
// JavaScript - 运行时才发现错误
function greet(name) {
    return "Hello, " + name.toUpperCase();
}

greet(123); // 运行时错误：name.toUpperCase is not a function

// TypeScript - 编译时发现错误
function greet(name: string): string {
    return "Hello, " + name.toUpperCase();
}

greet(123); // 编译错误：Argument of type 'number' is not assignable to parameter of type 'string'
```

## 1.2 TypeScript的优势

### 1. 类型安全
```typescript
// 类型检查帮助避免常见错误
let count: number = 10;
count = "hello"; // 错误：Type 'string' is not assignable to type 'number'
```

### 2. 更好的开发体验
```typescript
interface User {
    id: number;
    name: string;
    email: string;
}

function getUserInfo(user: User) {
    // IDE会提供智能提示
    return `${user.name} (${user.email})`;
}
```

### 3. 重构支持
```typescript
// 重命名属性时，IDE会自动更新所有引用
class Person {
    constructor(public fullName: string) {} // 重命名这里
}

const person = new Person("John Doe");
console.log(person.fullName); // 这里也会自动更新
```

## 1.3 环境搭建

### 安装Node.js
1. 访问 [nodejs.org](https://nodejs.org/)
2. 下载并安装LTS版本
3. 验证安装：`node --version`

### 安装TypeScript
```bash
# 全局安装
npm install -g typescript

# 验证安装
tsc --version

# 项目本地安装（推荐）
npm install --save-dev typescript
```

### 配置开发环境

#### VS Code扩展推荐：
- TypeScript Importer
- Prettier - Code formatter
- ESLint
- Auto Rename Tag
- Bracket Pair Colorizer

#### 创建tsconfig.json
```bash
# 初始化TypeScript配置
tsc --init
```

## 1.4 第一个TypeScript程序

创建 `hello.ts` 文件：

```typescript
// hello.ts
function sayHello(name: string): string {
    return `Hello, ${name}!`;
}

const userName: string = "TypeScript";
console.log(sayHello(userName));
```

编译并运行：
```bash
# 编译
tsc hello.ts

# 运行
node hello.js
```

## 1.5 开发工作流

### 1. 监听模式
```bash
# 自动编译
tsc --watch
```

### 2. 使用ts-node直接运行
```bash
# 安装ts-node
npm install --save-dev ts-node

# 直接运行TypeScript文件
npx ts-node hello.ts
```

### 3. 配置package.json脚本
```json
{
  "scripts": {
    "build": "tsc",
    "dev": "ts-node src/index.ts",
    "start": "node dist/index.js"
  }
}
```

## 1.6 小结

本章我们学习了：
- TypeScript的基本概念和优势
- 开发环境的搭建
- 第一个TypeScript程序
- 基本的开发工作流

下一章我们将深入学习TypeScript的类型系统。
