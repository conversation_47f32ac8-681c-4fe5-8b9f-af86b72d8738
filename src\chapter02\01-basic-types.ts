/**
 * 第2章示例：基础类型系统
 * 演示TypeScript的各种基本类型
 */

// 1. 原始类型
console.log("=== 原始类型 ===");

// 布尔值
const isActive: boolean = true;
const isCompleted: boolean = false;
console.log("Boolean values:", { isActive, isCompleted });

// 数字类型
const decimal: number = 42;
const hex: number = 0xff;
const binary: number = 0b1010;
const octal: number = 0o744;
const float: number = 3.14;
console.log("Number values:", { decimal, hex, binary, octal, float });

// 字符串类型
const name: string = "TypeScript";
const greeting: string = `Hello, ${name}!`;
const multiline: string = `
    This is a
    multiline string
`;
console.log("String values:", { name, greeting, multiline });

// 2. 数组类型
console.log("\n=== 数组类型 ===");

const numbers: number[] = [1, 2, 3, 4, 5];
const fruits: Array<string> = ["apple", "banana", "orange"];
const mixed: (string | number)[] = ["hello", 42, "world", 100];

console.log("Arrays:", { numbers, fruits, mixed });

// 3. 元组类型
console.log("\n=== 元组类型 ===");

const person: [string, number, boolean] = ["Alice", 25, true];
const coordinate: [number, number] = [10, 20];

console.log("Tuples:", { person, coordinate });

// 访问元组元素
console.log(`Person name: ${person[0]}, age: ${person[1]}, active: ${person[2]}`);

// 4. 枚举类型
console.log("\n=== 枚举类型 ===");

// 数字枚举
enum Color {
    Red,
    Green,
    Blue
}

// 字符串枚举
enum Direction {
    Up = "UP",
    Down = "DOWN",
    Left = "LEFT",
    Right = "RIGHT"
}

// 手动设置数字枚举值
enum HttpStatus {
    OK = 200,
    NotFound = 404,
    InternalServerError = 500
}

const favoriteColor: Color = Color.Blue;
const moveDirection: Direction = Direction.Up;
const responseStatus: HttpStatus = HttpStatus.OK;

console.log("Enums:", { favoriteColor, moveDirection, responseStatus });

// 5. Any 类型
console.log("\n=== Any 类型 ===");

let anything: any = 42;
anything = "Hello";
anything = true;
anything = { name: "Object" };
anything = [1, 2, 3];

console.log("Any type can hold anything:", anything);

// any类型的数组
const mixedArray: any[] = [1, "hello", true, { key: "value" }];
console.log("Mixed array:", mixedArray);

// 6. Unknown 类型
console.log("\n=== Unknown 类型 ===");

let userInput: unknown;
userInput = 5;
userInput = "Hello";

// 使用unknown类型需要类型检查
if (typeof userInput === "string") {
    console.log("User input as string:", userInput.toUpperCase());
}

if (typeof userInput === "number") {
    console.log("User input as number:", userInput * 2);
}

// 7. Void 类型
console.log("\n=== Void 类型 ===");

function logMessage(message: string): void {
    console.log("Log:", message);
    // 不返回任何值
}

logMessage("This function returns void");

// 8. Never 类型
console.log("\n=== Never 类型 ===");

function throwError(message: string): never {
    throw new Error(message);
}

function infiniteLoop(): never {
    while (true) {
        // 无限循环
        break; // 为了避免真的无限循环，这里break
    }
    throw new Error("This should never be reached");
}

// 9. Null 和 Undefined
console.log("\n=== Null 和 Undefined ===");

let nullValue: null = null;
let undefinedValue: undefined = undefined;

// 联合类型允许null和undefined
let nullable: string | null = null;
let optional: string | undefined = undefined;

nullable = "Now it has a value";
optional = "Now it's defined";

console.log("Nullable and optional:", { nullable, optional });

// 10. Object 类型
console.log("\n=== Object 类型 ===");

// 基本object类型
let obj: object = {};
obj = { name: "Alice" };
obj = [];
obj = function() {};

// 更具体的对象类型
let user: { name: string; age: number; email?: string } = {
    name: "John Doe",
    age: 30
};

user.email = "<EMAIL>"; // 可选属性可以后续赋值

console.log("Object types:", { obj, user });

export {
    Color,
    Direction,
    HttpStatus,
    logMessage,
    throwError
};
