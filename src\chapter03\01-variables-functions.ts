/**
 * 第3章示例：变量声明与函数
 * 演示TypeScript中的变量声明和函数定义
 */

// 1. 变量声明
console.log("=== 变量声明 ===");

// let 和 const
let mutableValue: number = 10;
const immutableValue: string = "TypeScript";

mutableValue = 20; // OK
// immutableValue = "JavaScript"; // 错误：Cannot assign to 'immutableValue'

console.log("Variables:", { mutableValue, immutableValue });

// 块级作用域
function demonstrateScope(): void {
    let outerVariable = "outer";
    
    if (true) {
        let innerVariable = "inner";
        const blockConstant = "block";
        console.log("Inside block:", { outerVariable, innerVariable, blockConstant });
    }
    
    // console.log(innerVariable); // 错误：innerVariable未定义
    console.log("Outside block:", { outerVariable });
}

demonstrateScope();

// 2. 解构赋值
console.log("\n=== 解构赋值 ===");

// 数组解构
const numbers: number[] = [1, 2, 3, 4, 5];
const [first, second, ...rest] = numbers;
console.log("Array destructuring:", { first, second, rest });

// 对象解构
const person = {
    name: "Alice",
    age: 25,
    city: "New York",
    country: "USA"
};

const { name, age, ...address } = person;
console.log("Object destructuring:", { name, age, address });

// 重命名和默认值
const { name: userName, age: userAge, email = "not provided" } = person;
console.log("Renamed and default:", { userName, userAge, email });

// 3. 基本函数
console.log("\n=== 基本函数 ===");

// 函数声明
function add(x: number, y: number): number {
    return x + y;
}

// 函数表达式
const multiply = function(x: number, y: number): number {
    return x * y;
};

// 箭头函数
const divide = (x: number, y: number): number => {
    if (y === 0) {
        throw new Error("Division by zero");
    }
    return x / y;
};

// 简化的箭头函数
const square = (x: number): number => x * x;

console.log("Function results:", {
    add: add(10, 5),
    multiply: multiply(10, 5),
    divide: divide(10, 5),
    square: square(5)
});

// 4. 可选参数和默认参数
console.log("\n=== 可选参数和默认参数 ===");

function greet(name: string, greeting?: string): string {
    return `${greeting || "Hello"}, ${name}!`;
}

function createUser(name: string, age: number = 18, isActive: boolean = true): object {
    return { name, age, isActive };
}

// 剩余参数
function sum(...numbers: number[]): number {
    return numbers.reduce((total, num) => total + num, 0);
}

console.log("Optional and default parameters:", {
    greet1: greet("Alice"),
    greet2: greet("Bob", "Hi"),
    user1: createUser("Charlie"),
    user2: createUser("David", 25),
    user3: createUser("Eve", 30, false),
    sum: sum(1, 2, 3, 4, 5)
});

// 5. 函数类型
console.log("\n=== 函数类型 ===");

// 定义函数类型
type MathOperation = (a: number, b: number) => number;
type StringProcessor = (str: string) => string;

const subtract: MathOperation = (a, b) => a - b;
const modulo: MathOperation = (a, b) => a % b;

const toUpperCase: StringProcessor = (str) => str.toUpperCase();
const reverse: StringProcessor = (str) => str.split("").reverse().join("");

console.log("Function types:", {
    subtract: subtract(10, 3),
    modulo: modulo(10, 3),
    toUpperCase: toUpperCase("hello"),
    reverse: reverse("hello")
});

// 6. 函数重载
console.log("\n=== 函数重载 ===");

// 重载声明
function combine(a: string, b: string): string;
function combine(a: number, b: number): number;
function combine(a: boolean, b: boolean): boolean;

// 实现
function combine(a: any, b: any): any {
    if (typeof a === "string" && typeof b === "string") {
        return a + b;
    } else if (typeof a === "number" && typeof b === "number") {
        return a + b;
    } else if (typeof a === "boolean" && typeof b === "boolean") {
        return a && b;
    }
    throw new Error("Invalid arguments");
}

console.log("Function overloads:", {
    stringCombine: combine("Hello", " World"),
    numberCombine: combine(10, 20),
    booleanCombine: combine(true, false)
});

// 7. 高阶函数
console.log("\n=== 高阶函数 ===");

// 函数作为参数
function processArray<T>(
    array: T[],
    processor: (item: T, index: number) => T
): T[] {
    return array.map(processor);
}

const numberArray = [1, 2, 3, 4, 5];
const doubled = processArray(numberArray, (num) => num * 2);
const indexed = processArray(numberArray, (num, index) => num + index);

console.log("Higher-order functions:", { doubled, indexed });

// 函数作为返回值
function createMultiplier(factor: number): (num: number) => number {
    return (num: number) => num * factor;
}

function createValidator(minLength: number): (str: string) => boolean {
    return (str: string) => str.length >= minLength;
}

const double = createMultiplier(2);
const triple = createMultiplier(3);
const isValidPassword = createValidator(8);

console.log("Function factories:", {
    double5: double(5),
    triple5: triple(5),
    validPassword: isValidPassword("password123"),
    invalidPassword: isValidPassword("123")
});

// 8. 函数式编程概念
console.log("\n=== 函数式编程概念 ===");

// 纯函数
function pureAdd(x: number, y: number): number {
    return x + y; // 无副作用，相同输入总是相同输出
}

// 柯里化
function curry<A, B, C>(fn: (a: A, b: B) => C): (a: A) => (b: B) => C {
    return (a: A) => (b: B) => fn(a, b);
}

const addTwoNumbers = (a: number, b: number) => a + b;
const curriedAdd = curry(addTwoNumbers);
const addFive = curriedAdd(5);

// 函数组合
function compose<A, B, C>(f: (b: B) => C, g: (a: A) => B): (a: A) => C {
    return (a: A) => f(g(a));
}

const addOne = (x: number) => x + 1;
const multiplyByTwo = (x: number) => x * 2;
const addOneThenDouble = compose(multiplyByTwo, addOne);

console.log("Functional programming:", {
    pureAdd: pureAdd(3, 4),
    curriedAdd: addFive(3),
    composed: addOneThenDouble(5)
});

export {
    add,
    multiply,
    divide,
    square,
    greet,
    createUser,
    sum,
    MathOperation,
    StringProcessor,
    combine,
    processArray,
    createMultiplier,
    createValidator,
    curry,
    compose
};
