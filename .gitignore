# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist/
build/
out/
*.tsbuildinfo

# 测试覆盖率
coverage/
*.lcov

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 目录用于检测工具
.nyc_output

# Grunt中间存储
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1声明文件
typings/

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 微束缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env
.env.test
.env.production
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# 临时文件夹
tmp/
temp/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
*.lcov

# nyc测试覆盖率
.nyc_output

# Grunt中间存储
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# Snowpack依赖目录
web_modules/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 可选的stylelint缓存
.stylelintcache

# 微束缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next
out

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless/

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/

# TernJS端口文件
.tern-port

# Stores VSCode版本用于测试
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志
logs
*.log

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
