/**
 * 第2章示例：高级类型
 * 演示联合类型、交叉类型、类型断言等
 */

// 1. 联合类型 (Union Types)
console.log("=== 联合类型 ===");

type StringOrNumber = string | number;
type Theme = "light" | "dark" | "auto";

function formatId(id: StringOrNumber): string {
    if (typeof id === "string") {
        return `ID: ${id.toUpperCase()}`;
    } else {
        return `ID: ${id.toString().padStart(6, "0")}`;
    }
}

function setTheme(theme: Theme): void {
    console.log(`Setting theme to: ${theme}`);
}

console.log(formatId("abc123"));
console.log(formatId(456));
setTheme("dark");

// 2. 交叉类型 (Intersection Types)
console.log("\n=== 交叉类型 ===");

interface Person {
    name: string;
    age: number;
}

interface Employee {
    employeeId: string;
    department: string;
}

type PersonEmployee = Person & Employee;

const worker: PersonEmployee = {
    name: "<PERSON>",
    age: 28,
    employeeId: "EMP001",
    department: "Engineering"
};

console.log("Worker:", worker);

// 3. 类型断言 (Type Assertions)
console.log("\n=== 类型断言 ===");

// 使用 as 语法
let someValue: unknown = "Hello TypeScript";
let strLength: number = (someValue as string).length;

// 使用尖括号语法（在JSX中不可用）
let anotherValue: unknown = "Another string";
let anotherLength: number = (<string>anotherValue).length;

console.log("String lengths:", { strLength, anotherLength });

// DOM元素类型断言
function getElementValue(id: string): string {
    const element = document.getElementById(id) as HTMLInputElement;
    return element?.value || "";
}

// 4. 字面量类型 (Literal Types)
console.log("\n=== 字面量类型 ===");

// 字符串字面量类型
type ButtonSize = "small" | "medium" | "large";
type ButtonVariant = "primary" | "secondary" | "danger";

function createButton(size: ButtonSize, variant: ButtonVariant): string {
    return `Button: ${size} ${variant}`;
}

console.log(createButton("large", "primary"));

// 数字字面量类型
type DiceRoll = 1 | 2 | 3 | 4 | 5 | 6;

function rollDice(): DiceRoll {
    return (Math.floor(Math.random() * 6) + 1) as DiceRoll;
}

console.log("Dice roll:", rollDice());

// 布尔字面量类型
type Success = true;
type Failure = false;

function processResult(success: Success | Failure): string {
    return success ? "Operation succeeded" : "Operation failed";
}

console.log(processResult(true));
console.log(processResult(false));

// 5. 类型守卫 (Type Guards)
console.log("\n=== 类型守卫 ===");

function isString(value: unknown): value is string {
    return typeof value === "string";
}

function isNumber(value: unknown): value is number {
    return typeof value === "number";
}

function processValue(value: unknown): string {
    if (isString(value)) {
        return `String: ${value.toUpperCase()}`;
    } else if (isNumber(value)) {
        return `Number: ${value.toFixed(2)}`;
    } else {
        return "Unknown type";
    }
}

console.log(processValue("hello"));
console.log(processValue(42.567));
console.log(processValue(true));

// 6. 可辨识联合 (Discriminated Unions)
console.log("\n=== 可辨识联合 ===");

interface Square {
    kind: "square";
    size: number;
}

interface Rectangle {
    kind: "rectangle";
    width: number;
    height: number;
}

interface Circle {
    kind: "circle";
    radius: number;
}

type Shape = Square | Rectangle | Circle;

function calculateArea(shape: Shape): number {
    switch (shape.kind) {
        case "square":
            return shape.size * shape.size;
        case "rectangle":
            return shape.width * shape.height;
        case "circle":
            return Math.PI * shape.radius * shape.radius;
        default:
            // 确保所有情况都被处理
            const _exhaustiveCheck: never = shape;
            return _exhaustiveCheck;
    }
}

const square: Square = { kind: "square", size: 10 };
const rectangle: Rectangle = { kind: "rectangle", width: 15, height: 20 };
const circle: Circle = { kind: "circle", radius: 5 };

console.log("Areas:", {
    square: calculateArea(square),
    rectangle: calculateArea(rectangle),
    circle: calculateArea(circle)
});

// 7. 索引类型 (Index Types)
console.log("\n=== 索引类型 ===");

interface StringDictionary {
    [key: string]: string;
}

interface NumberDictionary {
    [key: string]: number;
}

const colors: StringDictionary = {
    red: "#FF0000",
    green: "#00FF00",
    blue: "#0000FF"
};

const scores: NumberDictionary = {
    math: 95,
    science: 87,
    english: 92
};

console.log("Dictionaries:", { colors, scores });

// 8. 条件类型简介
console.log("\n=== 条件类型简介 ===");

type NonNullable<T> = T extends null | undefined ? never : T;

type StringOrNull = string | null;
type JustString = NonNullable<StringOrNull>; // string

// 9. 映射类型简介
console.log("\n=== 映射类型简介 ===");

type Readonly<T> = {
    readonly [P in keyof T]: T[P];
};

type Optional<T> = {
    [P in keyof T]?: T[P];
};

interface User {
    id: number;
    name: string;
    email: string;
}

type ReadonlyUser = Readonly<User>;
type OptionalUser = Optional<User>;

const readonlyUser: ReadonlyUser = {
    id: 1,
    name: "John",
    email: "<EMAIL>"
};

// readonlyUser.name = "Jane"; // 错误：Cannot assign to 'name' because it is a read-only property

const optionalUser: OptionalUser = {
    id: 2
    // name 和 email 是可选的
};

console.log("Mapped types:", { readonlyUser, optionalUser });

export {
    StringOrNumber,
    Theme,
    PersonEmployee,
    ButtonSize,
    ButtonVariant,
    DiceRoll,
    Shape,
    Square,
    Rectangle,
    Circle,
    formatId,
    setTheme,
    createButton,
    rollDice,
    processValue,
    calculateArea,
    isString,
    isNumber
};
