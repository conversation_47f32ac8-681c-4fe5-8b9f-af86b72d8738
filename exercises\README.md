# TypeScript 练习题

这里包含了各个章节的练习题，帮助您巩固所学知识。

## 📋 练习说明

- 每个练习都有明确的要求和预期输出
- 尝试独立完成练习，然后查看solutions目录中的参考答案
- 练习难度从基础到高级递增
- 建议使用TypeScript严格模式完成练习

## 🎯 第1-3章：基础练习

### 练习1：类型注解基础
创建一个函数，接收用户信息并返回格式化的字符串。

**要求：**
- 函数名：`formatUserInfo`
- 参数：name (string), age (number), isStudent (boolean)
- 返回：格式化的用户信息字符串

**预期输出：**
```
formatUserInfo("Alice", 20, true) 
// 返回: "姓名：Alice，年龄：20岁，学生身份：是"
```

### 练习2：数组和元组操作
创建函数处理学生成绩数据。

**要求：**
- 定义学生信息元组类型：[姓名, 数学成绩, 英语成绩, 科学成绩]
- 创建函数计算平均分
- 创建函数找出最高分科目

### 练习3：联合类型和类型守卫
创建一个ID处理函数，可以处理字符串或数字类型的ID。

**要求：**
- 如果是数字，格式化为6位数字（前面补0）
- 如果是字符串，转换为大写
- 使用类型守卫确保类型安全

## 🎯 第4-6章：中级练习

### 练习4：接口设计
设计一个图书管理系统的接口。

**要求：**
- Book接口：包含id、title、author、publishYear、isAvailable
- Library接口：包含添加、删除、搜索、借阅图书的方法
- 实现Library接口的具体类

### 练习5：类继承和多态
创建一个形状类层次结构。

**要求：**
- 抽象基类Shape，包含抽象方法calculateArea()
- 具体类Circle、Rectangle、Triangle继承Shape
- 创建形状数组，计算总面积

### 练习6：泛型编程
实现一个通用的数据容器类。

**要求：**
- 泛型类Container<T>
- 支持添加、删除、查找、过滤操作
- 实现迭代器模式

## 🎯 第7-9章：高级练习

### 练习7：高级类型操作
使用TypeScript的高级类型特性。

**要求：**
- 实现深度只读类型DeepReadonly<T>
- 实现可选属性类型PartialBy<T, K>
- 实现条件类型NonNullable<T>

### 练习8：装饰器应用
创建一个日志装饰器系统。

**要求：**
- 方法装饰器记录函数调用
- 类装饰器添加元数据
- 属性装饰器验证数据

### 练习9：模块系统
设计一个模块化的计算器系统。

**要求：**
- 基础运算模块
- 高级运算模块
- 统一的导出接口

## 🎯 第10章：综合项目练习

### 练习10：Todo应用
创建一个完整的Todo应用。

**要求：**
- 使用TypeScript严格模式
- 实现CRUD操作
- 数据持久化
- 类型安全的API设计

**功能需求：**
- 添加、编辑、删除任务
- 标记任务完成状态
- 任务分类和过滤
- 数据验证和错误处理

## 📝 提交说明

1. 在exercises目录下创建对应的解答文件
2. 文件命名格式：`exercise-XX-description.ts`
3. 确保代码能够通过TypeScript编译
4. 添加必要的注释说明思路

## 🏆 评分标准

- **基础分（60分）**：功能实现正确
- **进阶分（20分）**：代码结构清晰，类型使用恰当
- **高级分（20分）**：使用高级TypeScript特性，代码优雅

## 💡 学习建议

1. **循序渐进**：从简单练习开始，逐步提高难度
2. **多思考**：不要急于查看答案，先独立思考
3. **多实践**：尝试不同的实现方式
4. **查文档**：遇到问题时查阅TypeScript官方文档
5. **写注释**：养成良好的代码注释习惯

## 🔗 相关资源

- [TypeScript官方文档](https://www.typescriptlang.org/docs/)
- [TypeScript Playground](https://www.typescriptlang.org/play)
- [TypeScript深入理解](https://jkchao.github.io/typescript-book-chinese/)

---

**开始您的TypeScript练习之旅吧！** 💪
