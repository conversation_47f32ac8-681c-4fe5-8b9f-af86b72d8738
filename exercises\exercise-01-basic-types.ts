/**
 * 练习1：基础类型和函数
 * 
 * 完成以下练习，巩固TypeScript基础类型和函数的使用
 */

// ==================== 练习1.1 ====================
// 创建一个函数，格式化用户信息
// 要求：使用适当的类型注解

// TODO: 实现formatUserInfo函数
// function formatUserInfo(name: ?, age: ?, isStudent: ?): ? {
//     // 实现代码
// }

// 测试用例
// console.log(formatUserInfo("Alice", 20, true));
// 预期输出: "姓名：Alice，年龄：20岁，学生身份：是"

// console.log(formatUserInfo("Bob", 25, false));
// 预期输出: "姓名：Bob，年龄：25岁，学生身份：否"

// ==================== 练习1.2 ====================
// 创建一个函数，计算数组中数字的统计信息
// 要求：返回最大值、最小值、平均值、总和

// TODO: 定义返回类型接口
// interface NumberStats {
//     // 定义属性
// }

// TODO: 实现calculateStats函数
// function calculateStats(numbers: ?): ? {
//     // 实现代码
// }

// 测试用例
// console.log(calculateStats([1, 2, 3, 4, 5]));
// 预期输出: { max: 5, min: 1, average: 3, sum: 15 }

// ==================== 练习1.3 ====================
// 创建一个函数，处理不同类型的ID
// 要求：使用联合类型和类型守卫

// TODO: 定义ID类型
// type ID = ?;

// TODO: 实现formatID函数
// function formatID(id: ?): ? {
//     // 如果是数字，格式化为6位数字（前面补0）
//     // 如果是字符串，转换为大写并添加前缀"ID-"
// }

// 测试用例
// console.log(formatID(123));        // 预期输出: "000123"
// console.log(formatID("abc123"));   // 预期输出: "ID-ABC123"

// ==================== 练习1.4 ====================
// 创建一个枚举和相关函数
// 要求：定义优先级枚举，并创建处理函数

// TODO: 定义Priority枚举
// enum Priority {
//     // 定义枚举值
// }

// TODO: 实现getPriorityLabel函数
// function getPriorityLabel(priority: ?): ? {
//     // 根据优先级返回中文标签
//     // Low -> "低", Medium -> "中", High -> "高", Critical -> "紧急"
// }

// TODO: 实现comparePriority函数
// function comparePriority(p1: ?, p2: ?): ? {
//     // 比较两个优先级，返回 -1, 0, 或 1
//     // 优先级：Critical > High > Medium > Low
// }

// 测试用例
// console.log(getPriorityLabel(Priority.High));     // 预期输出: "高"
// console.log(comparePriority(Priority.High, Priority.Low));  // 预期输出: 1

// ==================== 练习1.5 ====================
// 创建一个元组处理函数
// 要求：处理学生成绩数据

// TODO: 定义学生成绩元组类型
// type StudentGrade = ?; // [姓名, 数学, 英语, 科学]

// TODO: 实现calculateAverage函数
// function calculateAverage(student: ?): ? {
//     // 计算平均分，保留两位小数
// }

// TODO: 实现findBestSubject函数
// function findBestSubject(student: ?): ? {
//     // 返回最高分的科目名称
//     // 科目顺序：数学、英语、科学
// }

// 测试用例
// const student: StudentGrade = ["Alice", 85, 92, 78];
// console.log(calculateAverage(student));    // 预期输出: 85.00
// console.log(findBestSubject(student));     // 预期输出: "英语"

// ==================== 练习1.6 ====================
// 创建一个可选参数和默认参数的函数
// 要求：创建用户配置函数

// TODO: 定义UserConfig接口
// interface UserConfig {
//     // 定义配置属性
// }

// TODO: 实现createUserConfig函数
// function createUserConfig(
//     username: ?,
//     theme?: ?,
//     language?: ?,
//     notifications?: ?
// ): ? {
//     // 创建用户配置对象，使用默认值
//     // 默认值：theme="light", language="zh-CN", notifications=true
// }

// 测试用例
// console.log(createUserConfig("alice"));
// 预期输出: { username: "alice", theme: "light", language: "zh-CN", notifications: true }

// console.log(createUserConfig("bob", "dark", "en-US"));
// 预期输出: { username: "bob", theme: "dark", language: "en-US", notifications: true }

// ==================== 提示 ====================
/*
提示：
1. 使用适当的类型注解，避免使用any
2. 利用TypeScript的类型推断，但在必要时明确指定类型
3. 使用联合类型处理多种可能的输入
4. 使用类型守卫确保类型安全
5. 善用可选参数和默认参数简化函数调用
6. 使用枚举提高代码可读性
7. 利用元组表示固定长度和类型的数组

完成练习后，运行以下命令检查：
npx tsc exercises/exercise-01-basic-types.ts --noEmit
*/

export {}; // 使文件成为模块，避免全局作用域污染
