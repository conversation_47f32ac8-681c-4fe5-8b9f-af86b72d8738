# 第5章：类 (Classes)

## 5.1 类的基础

### 基本类定义

```typescript
class Person {
    name: string;
    age: number;

    constructor(name: string, age: number) {
        this.name = name;
        this.age = age;
    }

    greet(): string {
        return `Hello, I'm ${this.name} and I'm ${this.age} years old.`;
    }
}

const person = new Person("Alice", 25);
console.log(person.greet());
```

### 属性初始化

```typescript
class User {
    // 直接初始化
    id: number = 0;
    name: string = "";
    
    // 构造函数参数属性
    constructor(
        public email: string,
        private password: string,
        protected role: string = "user"
    ) {}
}
```

## 5.2 访问修饰符

### public（公共）

```typescript
class Animal {
    public name: string; // 默认为public
    
    constructor(name: string) {
        this.name = name;
    }
    
    public move(): void {
        console.log(`${this.name} is moving`);
    }
}
```

### private（私有）

```typescript
class BankAccount {
    private balance: number = 0;
    
    constructor(initialBalance: number) {
        this.balance = initialBalance;
    }
    
    public deposit(amount: number): void {
        this.balance += amount;
    }
    
    public getBalance(): number {
        return this.balance;
    }
    
    // 私有方法
    private validateAmount(amount: number): boolean {
        return amount > 0;
    }
}
```

### protected（受保护）

```typescript
class Vehicle {
    protected engine: string;
    
    constructor(engine: string) {
        this.engine = engine;
    }
    
    protected startEngine(): void {
        console.log(`${this.engine} engine started`);
    }
}

class Car extends Vehicle {
    start(): void {
        this.startEngine(); // 可以访问protected成员
    }
}
```

## 5.3 继承

### 基本继承

```typescript
class Animal {
    name: string;
    
    constructor(name: string) {
        this.name = name;
    }
    
    move(distance: number = 0): void {
        console.log(`${this.name} moved ${distance}m`);
    }
}

class Dog extends Animal {
    breed: string;
    
    constructor(name: string, breed: string) {
        super(name); // 调用父类构造函数
        this.breed = breed;
    }
    
    bark(): void {
        console.log("Woof! Woof!");
    }
    
    // 重写父类方法
    move(distance: number = 5): void {
        console.log("Running...");
        super.move(distance);
    }
}
```

## 5.4 抽象类

```typescript
abstract class Shape {
    abstract area(): number;
    abstract perimeter(): number;
    
    // 具体方法
    displayInfo(): void {
        console.log(`Area: ${this.area()}, Perimeter: ${this.perimeter()}`);
    }
}

class Circle extends Shape {
    constructor(private radius: number) {
        super();
    }
    
    area(): number {
        return Math.PI * this.radius * this.radius;
    }
    
    perimeter(): number {
        return 2 * Math.PI * this.radius;
    }
}

class Rectangle extends Shape {
    constructor(private width: number, private height: number) {
        super();
    }
    
    area(): number {
        return this.width * this.height;
    }
    
    perimeter(): number {
        return 2 * (this.width + this.height);
    }
}
```

## 5.5 静态成员

```typescript
class MathUtils {
    static PI: number = 3.14159;
    
    static calculateCircleArea(radius: number): number {
        return this.PI * radius * radius;
    }
    
    static max(a: number, b: number): number {
        return a > b ? a : b;
    }
}

// 使用静态成员
console.log(MathUtils.PI);
console.log(MathUtils.calculateCircleArea(5));
console.log(MathUtils.max(10, 20));
```

## 5.6 Getter 和 Setter

```typescript
class Temperature {
    private _celsius: number = 0;
    
    get celsius(): number {
        return this._celsius;
    }
    
    set celsius(value: number) {
        if (value < -273.15) {
            throw new Error("Temperature cannot be below absolute zero");
        }
        this._celsius = value;
    }
    
    get fahrenheit(): number {
        return (this._celsius * 9/5) + 32;
    }
    
    set fahrenheit(value: number) {
        this.celsius = (value - 32) * 5/9;
    }
}

const temp = new Temperature();
temp.celsius = 25;
console.log(temp.fahrenheit); // 77
```

## 5.7 只读属性

```typescript
class Book {
    readonly isbn: string;
    readonly title: string;
    
    constructor(isbn: string, title: string) {
        this.isbn = isbn;
        this.title = title;
    }
    
    // isbn和title在构造后不能修改
}

// 参数属性的只读版本
class Author {
    constructor(
        public readonly name: string,
        public readonly birthYear: number
    ) {}
}
```

## 5.8 类实现接口

```typescript
interface Drivable {
    speed: number;
    drive(): void;
}

interface Flyable {
    altitude: number;
    fly(): void;
}

class FlyingCar implements Drivable, Flyable {
    speed: number = 0;
    altitude: number = 0;
    
    drive(): void {
        this.speed = 60;
        console.log(`Driving at ${this.speed} km/h`);
    }
    
    fly(): void {
        this.altitude = 1000;
        console.log(`Flying at ${this.altitude} meters`);
    }
}
```

## 5.9 泛型类

```typescript
class GenericContainer<T> {
    private items: T[] = [];
    
    add(item: T): void {
        this.items.push(item);
    }
    
    get(index: number): T | undefined {
        return this.items[index];
    }
    
    getAll(): T[] {
        return [...this.items];
    }
    
    size(): number {
        return this.items.length;
    }
}

const stringContainer = new GenericContainer<string>();
stringContainer.add("Hello");
stringContainer.add("World");

const numberContainer = new GenericContainer<number>();
numberContainer.add(1);
numberContainer.add(2);
```

## 5.10 类的高级特性

### 方法重载

```typescript
class Calculator {
    add(a: number, b: number): number;
    add(a: string, b: string): string;
    add(a: any, b: any): any {
        return a + b;
    }
}
```

### 类表达式

```typescript
const MyClass = class {
    name: string;
    
    constructor(name: string) {
        this.name = name;
    }
};

// 命名类表达式
const NamedClass = class MyNamedClass {
    // ...
};
```

### 混入 (Mixins)

```typescript
// 混入函数
function Timestamped<T extends new(...args: any[]) => {}>(Base: T) {
    return class extends Base {
        timestamp = Date.now();
        
        getTimestamp() {
            return new Date(this.timestamp);
        }
    };
}

function Activatable<T extends new(...args: any[]) => {}>(Base: T) {
    return class extends Base {
        isActive = false;
        
        activate() {
            this.isActive = true;
        }
        
        deactivate() {
            this.isActive = false;
        }
    };
}

// 基类
class User {
    constructor(public name: string) {}
}

// 应用混入
const TimestampedUser = Timestamped(User);
const ActivatableUser = Activatable(User);
const TimestampedActivatableUser = Timestamped(Activatable(User));
```

## 5.11 装饰器预览

```typescript
// 注意：需要启用experimentalDecorators
function sealed(constructor: Function) {
    Object.seal(constructor);
    Object.seal(constructor.prototype);
}

@sealed
class Greeter {
    greeting: string;
    
    constructor(message: string) {
        this.greeting = message;
    }
    
    greet() {
        return `Hello, ${this.greeting}`;
    }
}
```

## 5.12 实际应用示例

### 数据模型类

```typescript
class UserModel {
    constructor(
        public readonly id: number,
        public name: string,
        public email: string,
        private _password: string
    ) {}
    
    // 密码验证
    validatePassword(password: string): boolean {
        return this._password === password;
    }
    
    // 更新密码
    updatePassword(oldPassword: string, newPassword: string): boolean {
        if (this.validatePassword(oldPassword)) {
            this._password = newPassword;
            return true;
        }
        return false;
    }
    
    // 获取用户信息（不包含密码）
    getPublicInfo() {
        return {
            id: this.id,
            name: this.name,
            email: this.email
        };
    }
}
```

### 服务类

```typescript
abstract class BaseService<T> {
    protected items: T[] = [];
    
    abstract create(item: Omit<T, 'id'>): T;
    abstract update(id: number, updates: Partial<T>): T | null;
    
    getAll(): T[] {
        return [...this.items];
    }
    
    getById(id: number): T | null {
        return this.items.find(item => (item as any).id === id) || null;
    }
    
    delete(id: number): boolean {
        const index = this.items.findIndex(item => (item as any).id === id);
        if (index > -1) {
            this.items.splice(index, 1);
            return true;
        }
        return false;
    }
}

interface User {
    id: number;
    name: string;
    email: string;
}

class UserService extends BaseService<User> {
    private nextId = 1;
    
    create(userData: Omit<User, 'id'>): User {
        const user: User = {
            id: this.nextId++,
            ...userData
        };
        this.items.push(user);
        return user;
    }
    
    update(id: number, updates: Partial<User>): User | null {
        const user = this.getById(id);
        if (user) {
            Object.assign(user, updates);
            return user;
        }
        return null;
    }
}
```

## 5.13 小结

本章我们学习了：
- 类的基本定义和使用
- 访问修饰符（public、private、protected）
- 继承和方法重写
- 抽象类和静态成员
- Getter和Setter
- 类实现接口
- 泛型类和高级特性
- 实际应用示例

下一章我们将学习泛型 (Generics)。
