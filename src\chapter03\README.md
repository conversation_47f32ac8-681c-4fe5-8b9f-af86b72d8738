# 第3章：变量声明与函数

## 3.1 变量声明

### let 和 const

```typescript
// let 声明
let count: number = 0;
count = 10; // 可以重新赋值

// const 声明
const name: string = "TypeScript";
// name = "JavaScript"; // 错误：Cannot assign to 'name' because it is a constant

// const 对象的属性可以修改
const user = {
    name: "<PERSON>",
    age: 25
};
user.age = 26; // OK
user.name = "<PERSON>"; // OK
```

### 块级作用域

```typescript
function example() {
    if (true) {
        let blockScoped = "只在这个块中可见";
        const alsoBlockScoped = "我也是";
    }
    // console.log(blockScoped); // 错误：blockScoped未定义
}
```

### 解构赋值

```typescript
// 数组解构
const [first, second, ...rest] = [1, 2, 3, 4, 5];

// 对象解构
const { name, age } = { name: "<PERSON>", age: 25, city: "New York" };

// 重命名
const { name: user<PERSON><PERSON>, age: userAge } = { name: "<PERSON>", age: 30 };

// 默认值
const { x = 0, y = 0 } = { x: 10 };
```

## 3.2 函数基础

### 函数声明

```typescript
// 基本函数声明
function add(x: number, y: number): number {
    return x + y;
}

// 函数表达式
const multiply = function(x: number, y: number): number {
    return x * y;
};

// 箭头函数
const divide = (x: number, y: number): number => x / y;
```

### 可选参数和默认参数

```typescript
// 可选参数
function greet(name: string, greeting?: string): string {
    return `${greeting || "Hello"}, ${name}!`;
}

// 默认参数
function createUser(name: string, age: number = 18): object {
    return { name, age };
}

// 剩余参数
function sum(...numbers: number[]): number {
    return numbers.reduce((total, num) => total + num, 0);
}
```

## 3.3 函数类型

### 函数类型定义

```typescript
// 函数类型
type MathOperation = (a: number, b: number) => number;

const add: MathOperation = (a, b) => a + b;
const subtract: MathOperation = (a, b) => a - b;

// 函数签名
interface Calculator {
    (operation: string, a: number, b: number): number;
}

const calculator: Calculator = (operation, a, b) => {
    switch (operation) {
        case "add": return a + b;
        case "subtract": return a - b;
        default: throw new Error("Unknown operation");
    }
};
```

### 函数重载

```typescript
// 函数重载声明
function combine(a: string, b: string): string;
function combine(a: number, b: number): number;
function combine(a: boolean, b: boolean): boolean;

// 函数实现
function combine(a: any, b: any): any {
    return a + b;
}

// 使用
const result1 = combine("Hello", " World"); // string
const result2 = combine(10, 20); // number
const result3 = combine(true, false); // boolean
```

## 3.4 高阶函数

### 函数作为参数

```typescript
function processArray<T>(
    array: T[],
    callback: (item: T, index: number) => T
): T[] {
    return array.map(callback);
}

const numbers = [1, 2, 3, 4, 5];
const doubled = processArray(numbers, (num) => num * 2);
```

### 函数作为返回值

```typescript
function createMultiplier(factor: number): (num: number) => number {
    return (num: number) => num * factor;
}

const double = createMultiplier(2);
const triple = createMultiplier(3);

console.log(double(5)); // 10
console.log(triple(5)); // 15
```

## 3.5 this 类型

### 显式 this 参数

```typescript
interface Card {
    suit: string;
    card: number;
}

interface Deck {
    suits: string[];
    cards: number[];
    createCardPicker(this: Deck): () => Card;
}

const deck: Deck = {
    suits: ["hearts", "spades", "clubs", "diamonds"],
    cards: Array(52),
    createCardPicker: function(this: Deck) {
        return () => {
            let pickedCard = Math.floor(Math.random() * 52);
            let pickedSuit = Math.floor(pickedCard / 13);
            
            return {
                suit: this.suits[pickedSuit],
                card: pickedCard % 13
            };
        };
    }
};
```

### this 参数在回调函数中

```typescript
interface UIElement {
    addClickListener(onclick: (this: void, e: Event) => void): void;
}

class Handler {
    info: string = "Handler info";
    
    onClickBad(this: Handler, e: Event) {
        // 这里的this类型是Handler
        this.info = e.type;
    }
    
    onClickGood = (e: Event) => {
        // 箭头函数不会改变this的指向
        this.info = e.type;
    }
}
```

## 3.6 函数式编程概念

### 纯函数

```typescript
// 纯函数：相同输入总是产生相同输出，无副作用
function pure(x: number, y: number): number {
    return x + y;
}

// 非纯函数：有副作用
let counter = 0;
function impure(x: number): number {
    counter++; // 副作用
    return x + counter;
}
```

### 柯里化

```typescript
// 柯里化函数
function curry<A, B, C>(
    fn: (a: A, b: B) => C
): (a: A) => (b: B) => C {
    return (a: A) => (b: B) => fn(a, b);
}

const add = (a: number, b: number) => a + b;
const curriedAdd = curry(add);
const addFive = curriedAdd(5);

console.log(addFive(3)); // 8
```

### 组合函数

```typescript
// 函数组合
function compose<A, B, C>(
    f: (b: B) => C,
    g: (a: A) => B
): (a: A) => C {
    return (a: A) => f(g(a));
}

const addOne = (x: number) => x + 1;
const multiplyByTwo = (x: number) => x * 2;

const addOneThenMultiplyByTwo = compose(multiplyByTwo, addOne);
console.log(addOneThenMultiplyByTwo(3)); // 8
```

## 3.7 异步函数

### Promise 和 async/await

```typescript
// Promise 函数
function fetchData(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            if (url.startsWith("https://")) {
                resolve(`Data from ${url}`);
            } else {
                reject(new Error("Invalid URL"));
            }
        }, 1000);
    });
}

// async/await 函数
async function getData(url: string): Promise<string> {
    try {
        const data = await fetchData(url);
        return data.toUpperCase();
    } catch (error) {
        throw new Error(`Failed to fetch data: ${error.message}`);
    }
}

// 使用
getData("https://api.example.com")
    .then(data => console.log(data))
    .catch(error => console.error(error));
```

## 3.8 小结

本章我们学习了：
- 变量声明（let、const）和作用域
- 解构赋值
- 函数的各种声明方式
- 函数类型和重载
- 高阶函数和函数式编程概念
- this 类型
- 异步函数

下一章我们将学习接口（Interfaces）。
