/**
 * 练习1解答：基础类型和函数
 * 
 * 这是练习1的参考解答，展示了TypeScript基础类型和函数的正确使用方式
 */

// ==================== 练习1.1 解答 ====================
// 创建一个函数，格式化用户信息

function formatUserInfo(name: string, age: number, isStudent: boolean): string {
    const studentStatus = isStudent ? "是" : "否";
    return `姓名：${name}，年龄：${age}岁，学生身份：${studentStatus}`;
}

// 测试用例
console.log("=== 练习1.1 测试 ===");
console.log(formatUserInfo("Alice", 20, true));
// 输出: "姓名：Alice，年龄：20岁，学生身份：是"

console.log(formatUserInfo("Bob", 25, false));
// 输出: "姓名：Bob，年龄：25岁，学生身份：否"

// ==================== 练习1.2 解答 ====================
// 创建一个函数，计算数组中数字的统计信息

interface NumberStats {
    max: number;
    min: number;
    average: number;
    sum: number;
}

function calculateStats(numbers: number[]): NumberStats {
    if (numbers.length === 0) {
        throw new Error("数组不能为空");
    }
    
    const sum = numbers.reduce((acc, num) => acc + num, 0);
    const max = Math.max(...numbers);
    const min = Math.min(...numbers);
    const average = sum / numbers.length;
    
    return {
        max,
        min,
        average: Math.round(average * 100) / 100, // 保留两位小数
        sum
    };
}

// 测试用例
console.log("\n=== 练习1.2 测试 ===");
console.log(calculateStats([1, 2, 3, 4, 5]));
// 输出: { max: 5, min: 1, average: 3, sum: 15 }

console.log(calculateStats([10, 20, 30]));
// 输出: { max: 30, min: 10, average: 20, sum: 60 }

// ==================== 练习1.3 解答 ====================
// 创建一个函数，处理不同类型的ID

type ID = string | number;

function formatID(id: ID): string {
    if (typeof id === "number") {
        // 数字类型：格式化为6位数字（前面补0）
        return id.toString().padStart(6, "0");
    } else {
        // 字符串类型：转换为大写并添加前缀
        return `ID-${id.toUpperCase()}`;
    }
}

// 测试用例
console.log("\n=== 练习1.3 测试 ===");
console.log(formatID(123));        // 输出: "000123"
console.log(formatID("abc123"));   // 输出: "ID-ABC123"
console.log(formatID(999999));     // 输出: "999999"

// ==================== 练习1.4 解答 ====================
// 创建一个枚举和相关函数

enum Priority {
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

function getPriorityLabel(priority: Priority): string {
    switch (priority) {
        case Priority.Low:
            return "低";
        case Priority.Medium:
            return "中";
        case Priority.High:
            return "高";
        case Priority.Critical:
            return "紧急";
        default:
            throw new Error("未知的优先级");
    }
}

function comparePriority(p1: Priority, p2: Priority): number {
    if (p1 > p2) return 1;
    if (p1 < p2) return -1;
    return 0;
}

// 测试用例
console.log("\n=== 练习1.4 测试 ===");
console.log(getPriorityLabel(Priority.High));     // 输出: "高"
console.log(getPriorityLabel(Priority.Critical)); // 输出: "紧急"
console.log(comparePriority(Priority.High, Priority.Low));      // 输出: 1
console.log(comparePriority(Priority.Medium, Priority.High));   // 输出: -1
console.log(comparePriority(Priority.Low, Priority.Low));       // 输出: 0

// ==================== 练习1.5 解答 ====================
// 创建一个元组处理函数

type StudentGrade = [string, number, number, number]; // [姓名, 数学, 英语, 科学]

function calculateAverage(student: StudentGrade): number {
    const [, math, english, science] = student;
    const average = (math + english + science) / 3;
    return Math.round(average * 100) / 100; // 保留两位小数
}

function findBestSubject(student: StudentGrade): string {
    const [, math, english, science] = student;
    const subjects = ["数学", "英语", "科学"];
    const scores = [math, english, science];
    
    const maxScore = Math.max(...scores);
    const bestSubjectIndex = scores.indexOf(maxScore);
    
    return subjects[bestSubjectIndex];
}

// 测试用例
console.log("\n=== 练习1.5 测试 ===");
const student: StudentGrade = ["Alice", 85, 92, 78];
console.log(`学生：${student[0]}`);
console.log(`平均分：${calculateAverage(student)}`);    // 输出: 85.00
console.log(`最佳科目：${findBestSubject(student)}`);   // 输出: "英语"

const student2: StudentGrade = ["Bob", 95, 88, 90];
console.log(`\n学生：${student2[0]}`);
console.log(`平均分：${calculateAverage(student2)}`);   // 输出: 91.00
console.log(`最佳科目：${findBestSubject(student2)}`);  // 输出: "数学"

// ==================== 练习1.6 解答 ====================
// 创建一个可选参数和默认参数的函数

interface UserConfig {
    username: string;
    theme: string;
    language: string;
    notifications: boolean;
}

function createUserConfig(
    username: string,
    theme: string = "light",
    language: string = "zh-CN",
    notifications: boolean = true
): UserConfig {
    return {
        username,
        theme,
        language,
        notifications
    };
}

// 测试用例
console.log("\n=== 练习1.6 测试 ===");
console.log(createUserConfig("alice"));
// 输出: { username: "alice", theme: "light", language: "zh-CN", notifications: true }

console.log(createUserConfig("bob", "dark", "en-US"));
// 输出: { username: "bob", theme: "dark", language: "en-US", notifications: true }

console.log(createUserConfig("charlie", "dark", "ja-JP", false));
// 输出: { username: "charlie", theme: "dark", language: "ja-JP", notifications: false }

// ==================== 总结 ====================
console.log("\n=== 练习总结 ===");
console.log("✅ 所有练习完成！");
console.log("📚 本练习涵盖了以下TypeScript概念：");
console.log("   - 基本类型注解 (string, number, boolean)");
console.log("   - 函数类型和返回值类型");
console.log("   - 接口定义和使用");
console.log("   - 联合类型和类型守卫");
console.log("   - 枚举的定义和使用");
console.log("   - 元组类型和解构赋值");
console.log("   - 可选参数和默认参数");
console.log("   - 类型推断和显式类型声明");

export {
    formatUserInfo,
    NumberStats,
    calculateStats,
    ID,
    formatID,
    Priority,
    getPriorityLabel,
    comparePriority,
    StudentGrade,
    calculateAverage,
    findBestSubject,
    UserConfig,
    createUserConfig
};
