# 第7章：高级类型

## 7.1 模板字面量类型

TypeScript 4.1引入了模板字面量类型，允许我们创建基于字符串模板的类型。

### 基本模板字面量类型

```typescript
type World = "world";
type Greeting = `hello ${World}`; // "hello world"

type EmailLocaleIDs = "welcome_email" | "email_heading";
type FooterLocaleIDs = "footer_title" | "footer_sendoff";

type AllLocaleIDs = `${EmailLocaleIDs | FooterLocaleIDs}_id`;
// "welcome_email_id" | "email_heading_id" | "footer_title_id" | "footer_sendoff_id"
```

### 模板字面量类型的实际应用

```typescript
// CSS属性类型
type CSSProperties = {
    [K in `margin-${'top' | 'right' | 'bottom' | 'left'}`]: string;
} & {
    [K in `padding-${'top' | 'right' | 'bottom' | 'left'}`]: string;
};

// 事件处理器类型
type EventHandlers<T> = {
    [K in keyof T as `on${Capitalize<string & K>}`]: (event: T[K]) => void;
};

interface WindowEvents {
    click: MouseEvent;
    scroll: Event;
    resize: UIEvent;
}

type WindowEventHandlers = EventHandlers<WindowEvents>;
// {
//   onClick: (event: MouseEvent) => void;
//   onScroll: (event: Event) => void;
//   onResize: (event: UIEvent) => void;
// }
```

## 7.2 条件类型深入

### 分布式条件类型

```typescript
type ToArray<Type> = Type extends any ? Type[] : never;

type StrArrOrNumArr = ToArray<string | number>; // string[] | number[]

// 非分布式版本
type ToArrayNonDistributive<Type> = [Type] extends [any] ? Type[] : never;
type StrArrOrNumArrNonDistributive = ToArrayNonDistributive<string | number>; // (string | number)[]
```

### infer 关键字

```typescript
// 提取函数返回类型
type ReturnType<T> = T extends (...args: any[]) => infer R ? R : any;

// 提取数组元素类型
type ArrayElementType<T> = T extends (infer U)[] ? U : never;

// 提取Promise的值类型
type PromiseType<T> = T extends Promise<infer U> ? U : never;

// 提取函数参数类型
type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never;

// 使用示例
type Func = (a: string, b: number) => boolean;
type FuncReturnType = ReturnType<Func>; // boolean
type FuncParams = Parameters<Func>; // [string, number]
```

### 递归条件类型

```typescript
// 深度只读类型
type DeepReadonly<T> = {
    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

// 深度可选类型
type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 扁平化嵌套数组
type Flatten<T> = T extends (infer U)[] ? Flatten<U> : T;

type NestedArray = number[][][];
type FlatArray = Flatten<NestedArray>; // number
```

## 7.3 映射类型高级用法

### 键重映射

```typescript
// 移除特定前缀
type RemovePrefix<T, Prefix extends string> = {
    [K in keyof T as K extends `${Prefix}${infer Rest}` ? Rest : K]: T[K];
};

interface ApiResponse {
    api_status: number;
    api_data: any;
    api_message: string;
    user_id: number;
}

type CleanResponse = RemovePrefix<ApiResponse, "api_">;
// {
//   status: number;
//   data: any;
//   message: string;
//   user_id: number;
// }

// 添加前缀
type AddPrefix<T, Prefix extends string> = {
    [K in keyof T as `${Prefix}${string & K}`]: T[K];
};

// Getters类型
type Getters<T> = {
    [K in keyof T as `get${Capitalize<string & K>}`]: () => T[K];
};

interface Person {
    name: string;
    age: number;
}

type PersonGetters = Getters<Person>;
// {
//   getName: () => string;
//   getAge: () => number;
// }
```

### 过滤属性

```typescript
// 过滤出特定类型的属性
type FilterByType<T, U> = {
    [K in keyof T as T[K] extends U ? K : never]: T[K];
};

interface Example {
    a: string;
    b: number;
    c: boolean;
    d: string;
}

type StringProperties = FilterByType<Example, string>; // { a: string; d: string; }
type NumberProperties = FilterByType<Example, number>; // { b: number; }

// 过滤出函数属性
type FunctionPropertyNames<T> = {
    [K in keyof T]: T[K] extends Function ? K : never;
}[keyof T];

type FunctionProperties<T> = Pick<T, FunctionPropertyNames<T>>;

class MyClass {
    prop1: string = "";
    prop2: number = 0;
    method1(): void {}
    method2(x: number): string { return ""; }
}

type MyClassMethods = FunctionProperties<MyClass>;
// {
//   method1: () => void;
//   method2: (x: number) => string;
// }
```

## 7.4 类型操作工具

### 高级工具类型

```typescript
// 严格的Pick类型（确保键存在）
type StrictPick<T, K extends keyof T> = {
    [P in K]: T[P];
};

// 可选的Pick类型
type OptionalPick<T, K extends keyof T> = {
    [P in K]?: T[P];
};

// 部分Required类型
type PartialRequired<T, K extends keyof T> = T & Required<Pick<T, K>>;

// 排除null和undefined的类型
type NonNullable<T> = T extends null | undefined ? never : T;

// 深度非空类型
type DeepNonNullable<T> = {
    [P in keyof T]: T[P] extends object ? DeepNonNullable<T[P]> : NonNullable<T[P]>;
};

// 使用示例
interface User {
    id: number;
    name?: string;
    email?: string;
    profile?: {
        avatar?: string;
        bio?: string;
    };
}

type UserWithRequiredName = PartialRequired<User, "name">;
// {
//   id: number;
//   name: string; // 现在是必需的
//   email?: string;
//   profile?: { avatar?: string; bio?: string; };
// }
```

### 类型谓词和类型守卫

```typescript
// 自定义类型守卫
function isString(value: unknown): value is string {
    return typeof value === "string";
}

function isNumber(value: unknown): value is number {
    return typeof value === "number";
}

function isObject(value: unknown): value is object {
    return typeof value === "object" && value !== null;
}

// 断言函数
function assertIsString(value: unknown): asserts value is string {
    if (typeof value !== "string") {
        throw new Error("Expected string");
    }
}

function assertIsNumber(value: unknown): asserts value is number {
    if (typeof value !== "number") {
        throw new Error("Expected number");
    }
}

// 使用示例
function processValue(value: unknown) {
    if (isString(value)) {
        // value的类型现在是string
        console.log(value.toUpperCase());
    } else if (isNumber(value)) {
        // value的类型现在是number
        console.log(value.toFixed(2));
    }
}

function processValueWithAssertion(value: unknown) {
    assertIsString(value);
    // value的类型现在确定是string
    console.log(value.toUpperCase());
}
```

## 7.5 品牌类型 (Branded Types)

```typescript
// 创建品牌类型
type Brand<T, B> = T & { __brand: B };

type UserId = Brand<number, "UserId">;
type ProductId = Brand<number, "ProductId">;
type Email = Brand<string, "Email">;

// 创建品牌类型的工厂函数
function createUserId(id: number): UserId {
    return id as UserId;
}

function createProductId(id: number): ProductId {
    return id as ProductId;
}

function createEmail(email: string): Email {
    if (!email.includes("@")) {
        throw new Error("Invalid email");
    }
    return email as Email;
}

// 使用品牌类型
function getUserById(id: UserId): User | null {
    // 实现获取用户逻辑
    return null;
}

function getProductById(id: ProductId): Product | null {
    // 实现获取产品逻辑
    return null;
}

// 类型安全的使用
const userId = createUserId(123);
const productId = createProductId(456);

getUserById(userId); // OK
// getUserById(productId); // 错误：类型不匹配
// getUserById(123); // 错误：需要UserId类型
```

## 7.6 高级模式

### 构建器模式类型

```typescript
class QueryBuilder<T = {}> {
    private query: T = {} as T;
    
    select<K extends string>(
        fields: K[]
    ): QueryBuilder<T & { select: K[] }> {
        return new QueryBuilder<T & { select: K[] }>();
    }
    
    where<K extends string, V>(
        field: K,
        value: V
    ): QueryBuilder<T & { where: Record<K, V> }> {
        return new QueryBuilder<T & { where: Record<K, V> }>();
    }
    
    orderBy<K extends string>(
        field: K,
        direction: "asc" | "desc"
    ): QueryBuilder<T & { orderBy: { field: K; direction: "asc" | "desc" } }> {
        return new QueryBuilder<T & { orderBy: { field: K; direction: "asc" | "desc" } }>();
    }
    
    build(): T {
        return this.query;
    }
}

// 使用示例
const query = new QueryBuilder()
    .select(["name", "email"])
    .where("age", 25)
    .orderBy("name", "asc")
    .build();

// query的类型是：
// {
//   select: ("name" | "email")[];
//   where: { age: number };
//   orderBy: { field: "name"; direction: "asc" };
// }
```

### 状态机类型

```typescript
type State = "idle" | "loading" | "success" | "error";

type Event = 
    | { type: "FETCH" }
    | { type: "SUCCESS"; data: any }
    | { type: "ERROR"; error: string }
    | { type: "RESET" };

type StateMachine = {
    idle: {
        FETCH: "loading";
    };
    loading: {
        SUCCESS: "success";
        ERROR: "error";
    };
    success: {
        FETCH: "loading";
        RESET: "idle";
    };
    error: {
        FETCH: "loading";
        RESET: "idle";
    };
};

type ValidTransition<
    TState extends State,
    TEvent extends Event
> = TEvent["type"] extends keyof StateMachine[TState]
    ? StateMachine[TState][TEvent["type"]]
    : never;

function transition<
    TState extends State,
    TEvent extends Event
>(
    state: TState,
    event: TEvent
): ValidTransition<TState, TEvent> {
    // 实现状态转换逻辑
    return null as any;
}

// 使用示例
const newState1 = transition("idle", { type: "FETCH" }); // "loading"
const newState2 = transition("loading", { type: "SUCCESS", data: {} }); // "success"
// const invalid = transition("idle", { type: "SUCCESS", data: {} }); // 错误：无效转换
```

## 7.7 实际应用示例

### 类型安全的配置系统

```typescript
type ConfigSchema = {
    database: {
        host: string;
        port: number;
        username: string;
        password: string;
    };
    api: {
        baseUrl: string;
        timeout: number;
        retries: number;
    };
    features: {
        enableLogging: boolean;
        enableMetrics: boolean;
    };
};

type ConfigPath<T, K extends keyof T = keyof T> = 
    K extends string
        ? T[K] extends object
            ? `${K}` | `${K}.${ConfigPath<T[K]>}`
            : `${K}`
        : never;

type ConfigValue<T, P extends string> = 
    P extends `${infer K}.${infer Rest}`
        ? K extends keyof T
            ? ConfigValue<T[K], Rest>
            : never
        : P extends keyof T
            ? T[P]
            : never;

class ConfigManager<T> {
    constructor(private config: T) {}
    
    get<P extends ConfigPath<T>>(path: P): ConfigValue<T, P> {
        const keys = path.split('.');
        let value: any = this.config;
        
        for (const key of keys) {
            value = value[key];
        }
        
        return value;
    }
    
    set<P extends ConfigPath<T>>(path: P, value: ConfigValue<T, P>): void {
        const keys = path.split('.');
        let current: any = this.config;
        
        for (let i = 0; i < keys.length - 1; i++) {
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
    }
}

// 使用示例
const config = new ConfigManager<ConfigSchema>({
    database: {
        host: "localhost",
        port: 5432,
        username: "admin",
        password: "secret"
    },
    api: {
        baseUrl: "https://api.example.com",
        timeout: 5000,
        retries: 3
    },
    features: {
        enableLogging: true,
        enableMetrics: false
    }
});

const dbHost = config.get("database.host"); // string
const apiTimeout = config.get("api.timeout"); // number
// const invalid = config.get("database.invalid"); // 错误：路径不存在
```

## 7.8 小结

本章我们学习了：
- 模板字面量类型的使用
- 条件类型的高级应用
- 映射类型的键重映射和过滤
- 高级工具类型的创建
- 品牌类型和类型安全
- 复杂的类型模式和实际应用

下一章我们将学习装饰器 (Decorators)。
